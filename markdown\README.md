# Estrutura de Conteúdo CYPHER

Esta pasta contém a estrutura organizada de todos os módulos e tópicos do currículo CYPHER.

## 📁 Estrutura das Pastas

### FASE 1: FUNDAMENTOS
- **introducao-seguranca/**
  - conceitos-fundamentais.md
  - triade-cia.md
  - tipos-ataques.md
  - frameworks-seguranca.md

- **fundamentos-so/**
  - windows-fundamentals.md
  - linux-fundamentals.md
  - windows-avancado.md
  - linux-avancado.md

- **fundamentos-redes/**
  - conceitos-rede.md
  - protocolos-essenciais.md
  - analise-trafego.md
  - seguranca-rede.md

- **programacao-seguranca/**
  - python-fundamentals.md
  - python-seguranca.md
  - bash-scripting.md
  - git-versionamento.md

### FASE 2: RECONHECIMENTO
- **osint/**
  - introducao-osint.md
  - ferramentas-basicas.md
  - osint-avancado.md
  - osint-profissional.md

- **analise-vulnerabilidades/**
  - conceitos-vulnerabilidade.md
  - ferramentas-scanning.md
  - scanning-avancado.md
  - vulnerability-management.md

- **engenharia-social/**
  - psicologia-seguranca.md
  - tipos-ataques.md
  - simulacoes-eticas.md
  - awareness-treinamento.md

### FASE 3: WEB SECURITY
- **desenvolvimento-web/**
  - tecnologias-web.md
  - arquitetura-web.md
  - desenvolvimento-backend.md

- **owasp-top10/**
  - owasp-top10-parte1.md
  - ferramentas-basicas.md
  - owasp-top10-parte2.md
  - vulnerabilidades-avancadas.md

- **apis-modernas/**
  - api-security.md
  - tecnologias-modernas.md

### FASE 4: INFRAESTRUTURA
- **seguranca-redes/**
- **seguranca-windows/**
- **seguranca-linux/**

### FASE 5: CLOUD SECURITY
- **cloud-computing/**
- **container-kubernetes/**

### FASE 6: MOBILE SECURITY
- **android-security/**
- **ios-security/**

### FASE 7: FERRAMENTAS
- **ferramentas-pentest/**
- **siem-deteccao/**

### FASE 8: ESPECIALIZAÇÃO
- **malware-analysis/**
- **digital-forensics/**
- **exploit-development/**

### FASE 9: CERTIFICAÇÕES
- **certificacoes/**
- **desenvolvimento-carreira/**

## 📝 Formato dos Arquivos

Cada arquivo .md segue o padrão:

```markdown
# Título do Tópico

> **Módulo**: X.Y Nome do Módulo  
> **Fase**: FASE X - NOME  
> **Tópico**: Nome do Tópico

## Objetivos de Aprendizagem

Ao final deste tópico, você será capaz de:
- [ ] Objetivo 1
- [ ] Objetivo 2
- [ ] Objetivo 3
- [ ] Objetivo 4

## Conteúdo

*[Conteúdo a ser preenchido]*

## Exercícios Práticos

*[Exercícios a serem definidos]*

## Recursos Adicionais

*[Links e materiais complementares]*

---
**Navegação**: [Anterior](arquivo-anterior.md) | **Próximo**: [Próximo](proximo-arquivo.md)
```

### FASE 4: INFRAESTRUTURA (12 arquivos)
- **seguranca-redes/** (4 arquivos)
  - hardening-rede.md
  - protocolos-seguros.md
  - ataques-rede.md
  - tecnicas-avancadas.md

- **seguranca-windows/** (4 arquivos)
  - hardening-windows.md
  - active-directory-basics.md
  - windows-security.md
  - advanced-windows-attacks.md

- **seguranca-linux/** (4 arquivos)
  - linux-hardening.md
  - monitoring-logs.md
  - linux-security.md
  - advanced-linux-security.md

### FASE 5: CLOUD SECURITY (7 arquivos)
- **cloud-computing/** (4 arquivos)
  - cloud-basics.md
  - cloud-security-fundamentals.md
  - aws-security.md
  - azure-security.md

- **container-kubernetes/** (3 arquivos)
  - container-basics.md
  - container-security.md
  - kubernetes-security.md

### FASE 6: MOBILE SECURITY (4 arquivos)
- **android-security/** (3 arquivos)
  - android-fundamentals.md
  - static-analysis.md
  - dynamic-analysis.md

- **ios-security/** (1 arquivo)
  - ios-fundamentals.md

### FASE 7: FERRAMENTAS (5 arquivos)
- **ferramentas-pentest/** (3 arquivos)
  - kali-linux.md
  - metasploit-framework.md
  - automacao.md

- **siem-deteccao/** (2 arquivos)
  - siem-fundamentals.md
  - threat-hunting.md

### FASE 8: ESPECIALIZAÇÃO (7 arquivos)
- **malware-analysis/** (3 arquivos)
  - static-analysis.md
  - dynamic-analysis.md
  - reverse-engineering.md

- **digital-forensics/** (2 arquivos)
  - forensics-fundamentals.md
  - network-forensics.md

- **exploit-development/** (2 arquivos)
  - assembly-language.md
  - buffer-overflows.md

### FASE 9: CERTIFICAÇÕES (6 arquivos)
- **certificacoes/** (4 arquivos)
  - comptia-security-plus.md
  - ceh-certified-ethical-hacker.md
  - cissp.md
  - oscp.md

- **desenvolvimento-carreira/** (2 arquivos)
  - soft-skills.md
  - portfolio-development.md

## 🎯 Status

- ✅ **FASE 1**: 16 arquivos .md vazios
- ✅ **FASE 2**: 12 arquivos .md vazios
- ✅ **FASE 3**: 7 arquivos .md vazios
- ✅ **FASE 4**: 12 arquivos .md vazios
- ✅ **FASE 5**: 7 arquivos .md vazios
- ✅ **FASE 6**: 4 arquivos .md vazios
- ✅ **FASE 7**: 5 arquivos .md vazios
- ✅ **FASE 8**: 7 arquivos .md vazios
- ✅ **FASE 9**: 6 arquivos .md vazios

## 📋 Características dos Arquivos

- ✅ **Todos os arquivos estão COMPLETAMENTE VAZIOS**
- ✅ **Prontos para preenchimento de conteúdo**
- ✅ **Estrutura hierárquica organizada**
- ✅ **Nomes padronizados e descritivos**

---

**🎉 TOTAL: 78 arquivos .md vazios criados em estrutura hierárquica completa!**
