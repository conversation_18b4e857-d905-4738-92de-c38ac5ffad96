# Estrutura de Conteúdo CYPHER

Esta pasta contém a estrutura organizada de todos os módulos e tópicos do currículo CYPHER.

## 📁 Estrutura das Pastas

### FASE 1: FUNDAMENTOS
- **introducao-seguranca/**
  - conceitos-fundamentais.md
  - triade-cia.md
  - tipos-ataques.md
  - frameworks-seguranca.md

- **fundamentos-so/**
  - windows-fundamentals.md
  - linux-fundamentals.md
  - windows-avancado.md
  - linux-avancado.md

- **fundamentos-redes/**
  - conceitos-rede.md
  - protocolos-essenciais.md
  - analise-trafego.md
  - seguranca-rede.md

- **programacao-seguranca/**
  - python-fundamentals.md
  - python-seguranca.md
  - bash-scripting.md
  - git-versionamento.md

### FASE 2: RECONHECIMENTO
- **osint/**
  - introducao-osint.md
  - ferramentas-basicas.md
  - osint-avancado.md
  - osint-profissional.md

- **analise-vulnerabilidades/**
  - conceitos-vulnerabilidade.md
  - ferramentas-scanning.md
  - scanning-avancado.md
  - vulnerability-management.md

- **engenharia-social/**
  - psicologia-seguranca.md
  - tipos-ataques.md
  - simulacoes-eticas.md
  - awareness-treinamento.md

### FASE 3: WEB SECURITY
- **desenvolvimento-web/**
  - tecnologias-web.md
  - arquitetura-web.md
  - desenvolvimento-backend.md

- **owasp-top10/**
  - owasp-top10-parte1.md
  - ferramentas-basicas.md
  - owasp-top10-parte2.md
  - vulnerabilidades-avancadas.md

- **apis-modernas/**
  - api-security.md
  - tecnologias-modernas.md

### FASE 4: INFRAESTRUTURA
- **seguranca-redes/**
- **seguranca-windows/**
- **seguranca-linux/**

### FASE 5: CLOUD SECURITY
- **cloud-computing/**
- **container-kubernetes/**

### FASE 6: MOBILE SECURITY
- **android-security/**
- **ios-security/**

### FASE 7: FERRAMENTAS
- **ferramentas-pentest/**
- **siem-deteccao/**

### FASE 8: ESPECIALIZAÇÃO
- **malware-analysis/**
- **digital-forensics/**
- **exploit-development/**

### FASE 9: CERTIFICAÇÕES
- **certificacoes/**
- **desenvolvimento-carreira/**

## 📝 Formato dos Arquivos

Cada arquivo .md segue o padrão:

```markdown
# Título do Tópico

> **Módulo**: X.Y Nome do Módulo  
> **Fase**: FASE X - NOME  
> **Tópico**: Nome do Tópico

## Objetivos de Aprendizagem

Ao final deste tópico, você será capaz de:
- [ ] Objetivo 1
- [ ] Objetivo 2
- [ ] Objetivo 3
- [ ] Objetivo 4

## Conteúdo

*[Conteúdo a ser preenchido]*

## Exercícios Práticos

*[Exercícios a serem definidos]*

## Recursos Adicionais

*[Links e materiais complementares]*

---
**Navegação**: [Anterior](arquivo-anterior.md) | **Próximo**: [Próximo](proximo-arquivo.md)
```

## 🎯 Status

- ✅ **FASE 1**: Estrutura completa criada (16 arquivos)
- ✅ **FASE 2**: Estrutura completa criada (12 arquivos)
- ✅ **FASE 3**: Estrutura parcial criada (4 arquivos)
- 🔄 **FASES 4-9**: Pastas criadas, arquivos a serem adicionados

## 📋 Próximos Passos

1. Completar arquivos das FASES 3-9
2. Preencher conteúdo dos arquivos existentes
3. Adicionar exercícios práticos
4. Incluir recursos adicionais e links

---

**Total de arquivos criados**: 32+ arquivos .md organizados em estrutura hierárquica
