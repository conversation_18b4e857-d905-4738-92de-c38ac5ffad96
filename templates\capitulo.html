{% extends "base.html" %}

{% block title %}{{ chapter.title }} - CYPHER{% endblock %}
{% block description %}{{ chapter.title }} - Capítulo do sistema de aprendizado CYPHER{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/markdown.css') }}">
{% endblock %}

{% block content %}
<div class="min-h-screen bg-black text-white">
    <!-- Header -->
    <header class="border-b border-gray-800 p-4 bg-black/90 backdrop-blur-sm sticky top-0 z-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <a href="/" class="flex items-center gap-2 hover:text-gray-300 transition-colors">
                    <i data-lucide="terminal" class="w-6 h-6 text-white"></i>
                    <span class="text-xl font-bold">CYPHER</span>
                </a>
                <span class="text-gray-500 font-mono">/ {{ chapter.id }}</span>
            </div>
            <div class="flex items-center gap-4">
                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                    <i data-lucide="sun" class="w-4 h-4"></i>
                </button>
                <!-- Table of Contents Toggle -->
                <button id="toc-toggle" class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                    <i data-lucide="list" class="w-4 h-4"></i>
                </button>
                <!-- Back to Chapters -->
                <a href="/capitulos" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4"></i>
                </a>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar TOC -->
        <aside id="toc-sidebar" class="w-80 border-r border-gray-800 bg-black h-screen overflow-y-auto">
            <div class="p-4 border-b border-gray-800">
                <h2 class="text-lg font-bold text-white">ÍNDICE</h2>
                <p class="text-xs text-gray-500 font-mono mt-1">{{ chapter.title }}</p>
            </div>

            <nav class="p-4" id="table-of-contents">
                <!-- TOC will be generated by JavaScript -->
            </nav>

            <!-- Chapter Navigation -->
            <div class="p-4 border-t border-gray-800 mt-auto">
                <h3 class="text-sm font-bold text-white mb-3">NAVEGAÇÃO</h3>
                
                {% if prev_chapter %}
                <a href="/capitulos/{{ prev_chapter.id }}" 
                   class="block p-2 mb-2 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors">
                    <i data-lucide="chevron-left" class="w-3 h-3 inline mr-1"></i>
                    {{ prev_chapter.title }}
                </a>
                {% endif %}
                
                {% if next_chapter %}
                <a href="/capitulos/{{ next_chapter.id }}" 
                   class="block p-2 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors">
                    <i data-lucide="chevron-right" class="w-3 h-3 inline mr-1"></i>
                    {{ next_chapter.title }}
                </a>
                {% endif %}
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto">
            <article class="max-w-4xl mx-auto p-8">
                <!-- Chapter Header -->
                <header class="mb-8 pb-6 border-b border-gray-800">
                    <h1 class="text-4xl font-bold text-white mb-4">{{ chapter.title }}</h1>
                    <div class="flex items-center gap-4 text-sm text-gray-400 font-mono">
                        <span><i data-lucide="file-text" class="w-4 h-4 inline mr-1"></i>{{ chapter.id }}</span>
                        <span><i data-lucide="clock" class="w-4 h-4 inline mr-1"></i>Atualizado hoje</span>
                        <span><i data-lucide="eye" class="w-4 h-4 inline mr-1"></i>Modo de leitura</span>
                    </div>
                </header>

                <!-- Chapter Content -->
                <div class="markdown-content">
                    {{ chapter.content | safe }}
                </div>

                <!-- Chapter Footer -->
                <footer class="mt-12 pt-8 border-t border-gray-800">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            {% if prev_chapter %}
                            <a href="/capitulos/{{ prev_chapter.id }}" 
                               class="flex items-center gap-2 px-4 py-2 bg-gray-900 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                                <span class="text-sm">{{ prev_chapter.title }}</span>
                            </a>
                            {% endif %}
                        </div>
                        
                        <div class="flex items-center gap-4">
                            <button onclick="toggleBookmark()" 
                                    class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                                <i data-lucide="bookmark" class="w-4 h-4"></i>
                            </button>
                            <button onclick="shareChapter()" 
                                    class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                                <i data-lucide="share" class="w-4 h-4"></i>
                            </button>
                            <button onclick="printChapter()" 
                                    class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                                <i data-lucide="printer" class="w-4 h-4"></i>
                            </button>
                        </div>
                        
                        <div class="flex items-center gap-4">
                            {% if next_chapter %}
                            <a href="/capitulos/{{ next_chapter.id }}" 
                               class="flex items-center gap-2 px-4 py-2 bg-gray-900 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors">
                                <span class="text-sm">{{ next_chapter.title }}</span>
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Progress Indicator -->
                    <div class="mt-6 text-center">
                        {% set current_index = namespace(value=0) %}
                        {% for ch in chapters %}
                            {% if ch.id == chapter.id %}
                                {% set current_index.value = loop.index %}
                            {% endif %}
                        {% endfor %}
                        <div class="text-xs text-gray-500 font-mono">
                            Capítulo {{ current_index.value }} de {{ chapters|length }}
                        </div>
                        <div class="w-full bg-gray-800 h-1 rounded-full mt-2">
                            <div class="bg-green-500 h-1 rounded-full"
                                 style="width: {{ (current_index.value / chapters|length * 100)|round }}%"></div>
                        </div>
                    </div>
                </footer>
            </article>
        </main>
    </div>

    <!-- Floating Action Button -->
    <div class="fixed bottom-6 right-6 z-50">
        <button id="scroll-to-top" 
                class="p-3 bg-gray-900 border border-gray-700 rounded-full hover:bg-gray-800 transition-colors shadow-lg">
            <i data-lucide="arrow-up" class="w-5 h-5"></i>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/capitulo.js') }}"></script>
{% endblock %}
