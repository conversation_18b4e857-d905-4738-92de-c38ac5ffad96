# Python para Segurança

> **Módulo**: 1.4 Programação para Segurança  
> **Fase**: FASE 1 - FUNDAMENTOS  
> **Tópico**: Python para Segurança

## Objetivos de Aprendizagem

Ao final deste tópico, você será capaz de:
- [ ] Usar bibliotecas de segurança (requests, scapy, etc.)
- [ ] Criar scripts para automação de tarefas
- [ ] Desenvolver ferramentas básicas de análise
- [ ] Implementar boas práticas de código seguro

## Conteúdo

*[Conteúdo a ser preenchido]*

## Exercícios Práticos

*[Exercícios a serem definidos]*

## Recursos Adicionais

*[Links e materiais complementares]*

---
**Anterior**: [Python Fundamentals](python-fundamentals.md) | **Próximo**: [Bash Scripting](bash-scripting.md)
