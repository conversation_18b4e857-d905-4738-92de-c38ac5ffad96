# 1.2 Fundamentos de Sistemas Operacionais

> **📍 Localização no Currículo CYPHER**
> **FASE 1: FUNDAMENTOS** → **Módulo 1.2: Fundamentos de SO**
> Duração: 8-12 semanas | Pré-requisito: [1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca)

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Compreender a arquitetura básica de sistemas operacionais
- Identificar diferenças entre Windows e Linux
- Executar comandos básicos em ambos os sistemas
- Reconhecer aspectos de segurança em SOs
- Aplicar os conceitos da **Tríade CIA** em sistemas operacionais
- Preparar-se para **hardening avançado** nas fases posteriores

## Introdução aos Sistemas Operacionais

Um **Sistema Operacional (SO)** é o software fundamental que gerencia recursos de hardware e fornece serviços para programas de aplicação.

### Funções Principais
- 🖥️ **Gerenciamento de processos**
- 💾 **Gerenciamento de memória**
- 📁 **Sistema de arquivos**
- 🔌 **Gerenciamento de dispositivos**
- 🔐 **Controle de acesso e segurança**

## Windows Fundamentals

### Arquitetura do Windows
```
┌─────────────────────────────────┐
│        Aplicações               │
├─────────────────────────────────┤
│        Win32 API                │
├─────────────────────────────────┤
│        Kernel Mode              │
│  ┌─────────────┬─────────────┐  │
│  │   Kernel    │   Drivers   │  │
│  └─────────────┴─────────────┘  │
├─────────────────────────────────┤
│        Hardware                 │
└─────────────────────────────────┘
```

### Comandos Básicos do Windows

#### Navegação e Arquivos
```cmd
# Listar diretório
dir

# Mudar diretório
cd C:\Users

# Criar diretório
mkdir nova_pasta

# Copiar arquivo
copy arquivo.txt destino\

# Mover arquivo
move arquivo.txt destino\

# Deletar arquivo
del arquivo.txt
```

#### Informações do Sistema
```cmd
# Informações do sistema
systeminfo

# Processos em execução
tasklist

# Serviços
sc query

# Usuários logados
query user

# Configuração de rede
ipconfig /all
```

### Registro do Windows
O **Registry** é uma base de dados hierárquica que armazena configurações do sistema.

```cmd
# Abrir editor do registro
regedit

# Consultar registro via linha de comando
reg query HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion
```

## Linux Fundamentals

### Arquitetura do Linux
```
┌─────────────────────────────────┐
│     Applications/Shell          │
├─────────────────────────────────┤
│     System Libraries            │
├─────────────────────────────────┤
│     System Calls                │
├─────────────────────────────────┤
│     Linux Kernel                │
├─────────────────────────────────┤
│     Hardware                    │
└─────────────────────────────────┘
```

### Comandos Básicos do Linux

#### Navegação e Arquivos
```bash
# Listar diretório
ls -la

# Mudar diretório
cd /home/<USER>

# Diretório atual
pwd

# Criar diretório
mkdir nova_pasta

# Copiar arquivo
cp arquivo.txt /destino/

# Mover/renomear arquivo
mv arquivo.txt novo_nome.txt

# Deletar arquivo
rm arquivo.txt

# Deletar diretório
rm -rf diretorio/
```

#### Permissões e Propriedade
```bash
# Alterar permissões
chmod 755 arquivo.txt
chmod u+x script.sh

# Alterar proprietário
chown user:group arquivo.txt

# Ver permissões detalhadas
ls -l arquivo.txt
```

#### Informações do Sistema
```bash
# Informações do sistema
uname -a

# Processos em execução
ps aux

# Uso de memória
free -h

# Uso de disco
df -h

# Usuários logados
who

# Configuração de rede
ifconfig
ip addr show
```

### Sistema de Arquivos Linux

#### Estrutura de Diretórios
```
/
├── bin/        # Binários essenciais
├── boot/       # Arquivos de boot
├── dev/        # Dispositivos
├── etc/        # Configurações do sistema
├── home/       # Diretórios dos usuários
├── lib/        # Bibliotecas compartilhadas
├── opt/        # Software opcional
├── proc/       # Sistema de arquivos virtual
├── root/       # Diretório do root
├── sbin/       # Binários do sistema
├── tmp/        # Arquivos temporários
├── usr/        # Programas de usuário
└── var/        # Dados variáveis
```

## Aspectos de Segurança

### Controle de Acesso

#### Windows - UAC (User Account Control)
```cmd
# Executar como administrador
runas /user:Administrator comando

# Verificar privilégios
whoami /priv
```

#### Linux - sudo
```bash
# Executar como root
sudo comando

# Trocar para root
sudo su -

# Verificar privilégios
sudo -l
```

### Logs do Sistema

#### Windows Event Logs
```cmd
# Visualizar logs de segurança
eventvwr.msc

# Logs via PowerShell
Get-EventLog -LogName Security -Newest 10
```

#### Linux Logs
```bash
# Logs do sistema
tail -f /var/log/syslog

# Logs de autenticação
tail -f /var/log/auth.log

# Logs com journalctl (systemd)
journalctl -f
```

### Serviços e Processos

#### Windows Services
```cmd
# Listar serviços
sc query

# Parar serviço
sc stop "nome_servico"

# Iniciar serviço
sc start "nome_servico"
```

#### Linux Daemons
```bash
# Systemd
systemctl status ssh
systemctl stop ssh
systemctl start ssh
systemctl enable ssh

# SysV Init
service ssh status
service ssh restart
```

## Hardening Básico

### Windows
- Desabilitar serviços desnecessários
- Configurar Windows Firewall
- Aplicar patches de segurança
- Configurar políticas de senha

### Linux
- Desabilitar serviços desnecessários
- Configurar iptables/firewalld
- Manter sistema atualizado
- Configurar SSH adequadamente

```bash
# Exemplo: Configuração SSH segura
# /etc/ssh/sshd_config
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
```

## Exercícios Práticos

### Exercício 1: Comandos Básicos
Execute os seguintes comandos em seu sistema:

**Windows:**
```cmd
systeminfo | findstr "OS Name"
tasklist | findstr "explorer"
```

**Linux:**
```bash
uname -r
ps aux | grep bash
```

### Exercício 2: Análise de Logs
Identifique tentativas de login falhadas nos logs do sistema.

## Laboratório Virtual

Para praticar, recomendamos configurar:
- 🖥️ **VirtualBox** ou **VMware**
- 💿 **Windows 10/11** (versão de avaliação)
- 🐧 **Ubuntu 22.04 LTS** ou **Kali Linux**

## Recursos Adicionais

- 📚 [Microsoft Learn - Windows](https://learn.microsoft.com/windows/)
- 🐧 [Linux Journey](https://linuxjourney.com/)
- 🎓 [OverTheWire - Bandit](https://overthewire.org/wargames/bandit/)

## Conexões com o Currículo CYPHER

### 🔗 Aplicação dos Conceitos da FASE 1

Este módulo aplica diretamente os conceitos aprendidos em:
- **[1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca)** - Tríade CIA aplicada a SOs

### 🚀 Preparação para Módulos Avançados

Os conhecimentos deste capítulo são essenciais para:

#### FASE 1 - Próximos Módulos:
- **[1.3 Fundamentos de Redes](/capitulos/03-fundamentos-redes)** - Segurança de rede e protocolos
- **[1.4 Programação para Segurança](/capitulos/04-programacao-seguranca)** - Scripts para automação

#### FASE 4 - INFRAESTRUTURA:
- **4.2 Segurança Windows** - Hardening avançado do Windows
- **4.3 Segurança Linux** - Hardening avançado do Linux

#### FASE 7 - FERRAMENTAS:
- **7.1 Ferramentas de Pentest** - Kali Linux e ferramentas especializadas

#### FASE 8 - ESPECIALIZAÇÃO:
- **8.2 Digital Forensics** - Análise forense em sistemas Windows/Linux

### 🛠️ Laboratório Recomendado

Para praticar os conceitos deste módulo:

1. **VirtualBox/VMware** com:
   - Windows 10/11 (versão de avaliação)
   - Ubuntu 22.04 LTS
   - Kali Linux (preparação para FASE 7)

2. **Cenários de Prática**:
   - Configuração de usuários e permissões
   - Análise de logs de segurança
   - Hardening básico de ambos os sistemas

### 📋 Checklist de Progresso

Antes de avançar, certifique-se de que você:

- [ ] Domina comandos básicos em Windows e Linux
- [ ] Compreende sistemas de permissões
- [ ] Sabe analisar logs de segurança
- [ ] Consegue aplicar hardening básico
- [ ] Completou os exercícios práticos

## Próximos Passos

### 📚 Próximo Módulo: [1.3 Fundamentos de Redes](/capitulos/03-fundamentos-redes)

No próximo capítulo da **FASE 1**, exploraremos como os **Fundamentos de Redes** se integram com a segurança de sistemas, preparando você para análise de tráfego e segurança de infraestrutura.

### 🎯 Objetivos do Próximo Módulo:
- Protocolos de rede e segurança
- Análise de tráfego com Wireshark
- Conceitos de firewall e IDS/IPS
- Preparação para **FASE 2: RECONHECIMENTO**

---

**📊 Progresso na FASE 1**: 2/4 módulos concluídos
**⏱️ Tempo estimado de estudo**: 4-6 horas
**🎯 Nível de dificuldade**: ⭐⭐⭐☆☆
**🔗 Módulo ID**: `os-fundamentals` (CYPHER Database)
