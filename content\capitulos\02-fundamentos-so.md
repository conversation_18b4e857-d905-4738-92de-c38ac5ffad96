# 1.2 Fundamentos de Sistemas Operacionais

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Compreender a arquitetura básica de sistemas operacionais
- Identificar diferenças entre Windows e Linux
- Executar comandos básicos em ambos os sistemas
- Reconhecer aspectos de segurança em SOs

## Introdução aos Sistemas Operacionais

Um **Sistema Operacional (SO)** é o software fundamental que gerencia recursos de hardware e fornece serviços para programas de aplicação.

### Funções Principais
- 🖥️ **Gerenciamento de processos**
- 💾 **Gerenciamento de memória**
- 📁 **Sistema de arquivos**
- 🔌 **Gerenciamento de dispositivos**
- 🔐 **Controle de acesso e segurança**

## Windows Fundamentals

### Arquitetura do Windows
```
┌─────────────────────────────────┐
│        Aplicações               │
├─────────────────────────────────┤
│        Win32 API                │
├─────────────────────────────────┤
│        Kernel Mode              │
│  ┌─────────────┬─────────────┐  │
│  │   Kernel    │   Drivers   │  │
│  └─────────────┴─────────────┘  │
├─────────────────────────────────┤
│        Hardware                 │
└─────────────────────────────────┘
```

### Comandos Básicos do Windows

#### Navegação e Arquivos
```cmd
# Listar diretório
dir

# Mudar diretório
cd C:\Users

# Criar diretório
mkdir nova_pasta

# Copiar arquivo
copy arquivo.txt destino\

# Mover arquivo
move arquivo.txt destino\

# Deletar arquivo
del arquivo.txt
```

#### Informações do Sistema
```cmd
# Informações do sistema
systeminfo

# Processos em execução
tasklist

# Serviços
sc query

# Usuários logados
query user

# Configuração de rede
ipconfig /all
```

### Registro do Windows
O **Registry** é uma base de dados hierárquica que armazena configurações do sistema.

```cmd
# Abrir editor do registro
regedit

# Consultar registro via linha de comando
reg query HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion
```

## Linux Fundamentals

### Arquitetura do Linux
```
┌─────────────────────────────────┐
│     Applications/Shell          │
├─────────────────────────────────┤
│     System Libraries            │
├─────────────────────────────────┤
│     System Calls                │
├─────────────────────────────────┤
│     Linux Kernel                │
├─────────────────────────────────┤
│     Hardware                    │
└─────────────────────────────────┘
```

### Comandos Básicos do Linux

#### Navegação e Arquivos
```bash
# Listar diretório
ls -la

# Mudar diretório
cd /home/<USER>

# Diretório atual
pwd

# Criar diretório
mkdir nova_pasta

# Copiar arquivo
cp arquivo.txt /destino/

# Mover/renomear arquivo
mv arquivo.txt novo_nome.txt

# Deletar arquivo
rm arquivo.txt

# Deletar diretório
rm -rf diretorio/
```

#### Permissões e Propriedade
```bash
# Alterar permissões
chmod 755 arquivo.txt
chmod u+x script.sh

# Alterar proprietário
chown user:group arquivo.txt

# Ver permissões detalhadas
ls -l arquivo.txt
```

#### Informações do Sistema
```bash
# Informações do sistema
uname -a

# Processos em execução
ps aux

# Uso de memória
free -h

# Uso de disco
df -h

# Usuários logados
who

# Configuração de rede
ifconfig
ip addr show
```

### Sistema de Arquivos Linux

#### Estrutura de Diretórios
```
/
├── bin/        # Binários essenciais
├── boot/       # Arquivos de boot
├── dev/        # Dispositivos
├── etc/        # Configurações do sistema
├── home/       # Diretórios dos usuários
├── lib/        # Bibliotecas compartilhadas
├── opt/        # Software opcional
├── proc/       # Sistema de arquivos virtual
├── root/       # Diretório do root
├── sbin/       # Binários do sistema
├── tmp/        # Arquivos temporários
├── usr/        # Programas de usuário
└── var/        # Dados variáveis
```

## Aspectos de Segurança

### Controle de Acesso

#### Windows - UAC (User Account Control)
```cmd
# Executar como administrador
runas /user:Administrator comando

# Verificar privilégios
whoami /priv
```

#### Linux - sudo
```bash
# Executar como root
sudo comando

# Trocar para root
sudo su -

# Verificar privilégios
sudo -l
```

### Logs do Sistema

#### Windows Event Logs
```cmd
# Visualizar logs de segurança
eventvwr.msc

# Logs via PowerShell
Get-EventLog -LogName Security -Newest 10
```

#### Linux Logs
```bash
# Logs do sistema
tail -f /var/log/syslog

# Logs de autenticação
tail -f /var/log/auth.log

# Logs com journalctl (systemd)
journalctl -f
```

### Serviços e Processos

#### Windows Services
```cmd
# Listar serviços
sc query

# Parar serviço
sc stop "nome_servico"

# Iniciar serviço
sc start "nome_servico"
```

#### Linux Daemons
```bash
# Systemd
systemctl status ssh
systemctl stop ssh
systemctl start ssh
systemctl enable ssh

# SysV Init
service ssh status
service ssh restart
```

## Hardening Básico

### Windows
- Desabilitar serviços desnecessários
- Configurar Windows Firewall
- Aplicar patches de segurança
- Configurar políticas de senha

### Linux
- Desabilitar serviços desnecessários
- Configurar iptables/firewalld
- Manter sistema atualizado
- Configurar SSH adequadamente

```bash
# Exemplo: Configuração SSH segura
# /etc/ssh/sshd_config
Port 2222
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
```

## Exercícios Práticos

### Exercício 1: Comandos Básicos
Execute os seguintes comandos em seu sistema:

**Windows:**
```cmd
systeminfo | findstr "OS Name"
tasklist | findstr "explorer"
```

**Linux:**
```bash
uname -r
ps aux | grep bash
```

### Exercício 2: Análise de Logs
Identifique tentativas de login falhadas nos logs do sistema.

## Laboratório Virtual

Para praticar, recomendamos configurar:
- 🖥️ **VirtualBox** ou **VMware**
- 💿 **Windows 10/11** (versão de avaliação)
- 🐧 **Ubuntu 22.04 LTS** ou **Kali Linux**

## Recursos Adicionais

- 📚 [Microsoft Learn - Windows](https://learn.microsoft.com/windows/)
- 🐧 [Linux Journey](https://linuxjourney.com/)
- 🎓 [OverTheWire - Bandit](https://overthewire.org/wargames/bandit/)

## Próximos Passos

No próximo capítulo, exploraremos os **Fundamentos de Redes** e como eles se integram com a segurança de sistemas.

---

**Tempo estimado de estudo**: 4-6 horas  
**Nível de dificuldade**: ⭐⭐⭐☆☆
