# 2.1 OSINT - Introdução ao Reconhecimento Passivo

> **📍 Localização no Currículo CYPHER**  
> **FASE 2: RECONHECIMENTO** → **Módulo 2.1: OSINT**  
> Duração: 10-14 semanas | Pré-requisito: **FASE 1: FUNDAMENTOS** completa

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Compreender os fundamentos do OSINT (Open Source Intelligence)
- Aplicar técnicas de reconhecimento passivo
- Utilizar ferramentas especializadas de OSINT
- Automatizar coleta de informações com Python
- Manter-se dentro dos limites éticos e legais
- Preparar-se para **análise de vulnerabilidades** (módulo 2.2)

## O que é OSINT?

**OSINT (Open Source Intelligence)** é a coleta e análise de informações disponíveis publicamente para fins de inteligência. Em cibersegurança, é usado para:

- 🔍 **Reconhecimento** de alvos
- 📊 **Mapeamento** de infraestrutura
- 👥 **Identificação** de pessoas-chave
- 🌐 **Análise** de presença digital
- 🛡️ **Avaliação** de exposição de dados

### Aplicação dos Conceitos da FASE 1

O OSINT aplica diretamente os conhecimentos fundamentais:

#### Da [1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca):
- **Confidencialidade**: Identificar vazamentos de informação
- **Integridade**: Verificar autenticidade de dados
- **Disponibilidade**: Mapear serviços expostos

#### Da [1.3 Fundamentos de Redes](/capitulos/03-fundamentos-redes):
- Análise de DNS e subdomínios
- Mapeamento de infraestrutura de rede
- Identificação de serviços expostos

#### Da [1.4 Programação para Segurança](/capitulos/04-programacao-seguranca):
- Automação de coleta de dados
- Scripts para análise em massa
- Integração com APIs públicas

## Tipos de OSINT

### 1. OSINT Passivo
Coleta de informações **sem interação direta** com o alvo:
- Pesquisas em motores de busca
- Análise de redes sociais
- Consulta a bases de dados públicas
- Análise de metadados

### 2. OSINT Semi-Passivo
Interação **mínima e indireta** com o alvo:
- Consultas DNS
- Análise de certificados SSL
- Verificação de cabeçalhos HTTP

### 3. OSINT Ativo
Interação **direta** com o alvo (cuidado legal!):
- Port scanning
- Banner grabbing
- Enumeração de serviços

## Metodologia OSINT

### Framework OSINT Cycle

```
1. PLANEJAMENTO
   ↓
2. COLETA
   ↓
3. PROCESSAMENTO
   ↓
4. ANÁLISE
   ↓
5. DISSEMINAÇÃO
   ↓
6. FEEDBACK
```

### 1. Planejamento e Requisitos

```python
# Template de planejamento OSINT
osint_plan = {
    "target": "exemplo.com",
    "objectives": [
        "Mapear infraestrutura",
        "Identificar tecnologias",
        "Encontrar subdomínios",
        "Analisar funcionários"
    ],
    "scope": {
        "domains": ["exemplo.com", "*.exemplo.com"],
        "social_media": True,
        "employees": True,
        "technologies": True
    },
    "legal_constraints": [
        "Apenas fontes públicas",
        "Não interagir diretamente",
        "Respeitar robots.txt"
    ]
}
```

### 2. Coleta de Informações

#### Informações sobre Domínios

```bash
# Whois lookup
whois exemplo.com

# DNS enumeration
dig exemplo.com ANY
nslookup exemplo.com

# Subdomain enumeration
sublist3r -d exemplo.com
amass enum -d exemplo.com
```

#### Ferramentas Python para OSINT

```python
import requests
import dns.resolver
import whois
from bs4 import BeautifulSoup

def domain_osint(domain):
    """Coleta informações básicas sobre um domínio"""
    
    print(f"=== OSINT para {domain} ===")
    
    # 1. Whois information
    try:
        w = whois.whois(domain)
        print(f"Registrar: {w.registrar}")
        print(f"Data de criação: {w.creation_date}")
        print(f"Data de expiração: {w.expiration_date}")
    except Exception as e:
        print(f"Erro no whois: {e}")
    
    # 2. DNS records
    record_types = ['A', 'AAAA', 'MX', 'NS', 'TXT']
    for record_type in record_types:
        try:
            answers = dns.resolver.resolve(domain, record_type)
            print(f"{record_type} records:")
            for answer in answers:
                print(f"  {answer}")
        except:
            pass
    
    # 3. HTTP headers
    try:
        response = requests.get(f"http://{domain}", timeout=5)
        print("HTTP Headers:")
        for header, value in response.headers.items():
            print(f"  {header}: {value}")
    except Exception as e:
        print(f"Erro HTTP: {e}")

# Uso
domain_osint("exemplo.com")
```

## Ferramentas OSINT Essenciais

### 1. Reconhecimento de Domínios

#### Sublist3r
```bash
# Instalar
pip install sublist3r

# Usar
sublist3r -d exemplo.com -o subdomains.txt
```

#### Amass
```bash
# Instalar (Go required)
go install -v github.com/OWASP/Amass/v3/...@master

# Usar
amass enum -d exemplo.com
amass intel -d exemplo.com
```

### 2. Análise de Metadados

#### ExifTool
```bash
# Instalar
sudo apt install exiftool

# Analisar imagem
exiftool documento.pdf
exiftool foto.jpg
```

#### Python para Metadados
```python
from PIL import Image
from PIL.ExifTags import TAGS
import os

def extract_metadata(image_path):
    """Extrai metadados de uma imagem"""
    
    try:
        image = Image.open(image_path)
        exifdata = image.getexif()
        
        print(f"Metadados de {image_path}:")
        for tag_id in exifdata:
            tag = TAGS.get(tag_id, tag_id)
            data = exifdata.get(tag_id)
            
            if isinstance(data, bytes):
                data = data.decode()
            
            print(f"  {tag}: {data}")
            
    except Exception as e:
        print(f"Erro: {e}")

# Uso
extract_metadata("foto.jpg")
```

### 3. OSINT em Redes Sociais

#### Sherlock
```bash
# Instalar
git clone https://github.com/sherlock-project/sherlock.git
cd sherlock
pip install -r requirements.txt

# Usar
python sherlock.py usuario_alvo
```

#### Script Python para LinkedIn
```python
import requests
from bs4 import BeautifulSoup
import time

def linkedin_company_employees(company_name):
    """
    Busca funcionários de uma empresa no LinkedIn
    ATENÇÃO: Respeite os termos de uso do LinkedIn
    """
    
    # Esta é uma versão simplificada para fins educacionais
    # Em produção, use APIs oficiais quando disponíveis
    
    search_url = f"https://www.linkedin.com/search/results/people/?keywords={company_name}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    print(f"Buscando funcionários de {company_name}")
    print("NOTA: Use apenas para fins educacionais e éticos")
    
    # Em um cenário real, você implementaria:
    # 1. Autenticação adequada
    # 2. Rate limiting
    # 3. Respeito ao robots.txt
    # 4. Uso de APIs oficiais

# IMPORTANTE: Este é apenas um exemplo conceitual
```

## Automação OSINT com Python

### Script Completo de Reconhecimento

```python
#!/usr/bin/env python3
"""
CYPHER OSINT Automation Tool
Módulo: 2.1 OSINT
"""

import argparse
import json
import requests
import dns.resolver
import whois
from datetime import datetime

class OSINTCollector:
    def __init__(self, target):
        self.target = target
        self.results = {
            'target': target,
            'timestamp': datetime.now().isoformat(),
            'whois': {},
            'dns': {},
            'subdomains': [],
            'technologies': [],
            'social_media': {}
        }
    
    def collect_whois(self):
        """Coleta informações whois"""
        try:
            w = whois.whois(self.target)
            self.results['whois'] = {
                'registrar': str(w.registrar),
                'creation_date': str(w.creation_date),
                'expiration_date': str(w.expiration_date),
                'name_servers': [str(ns) for ns in w.name_servers] if w.name_servers else []
            }
            print(f"✅ Whois coletado para {self.target}")
        except Exception as e:
            print(f"❌ Erro no whois: {e}")
    
    def collect_dns(self):
        """Coleta registros DNS"""
        record_types = ['A', 'AAAA', 'MX', 'NS', 'TXT', 'CNAME']
        
        for record_type in record_types:
            try:
                answers = dns.resolver.resolve(self.target, record_type)
                self.results['dns'][record_type] = [str(answer) for answer in answers]
            except:
                self.results['dns'][record_type] = []
        
        print(f"✅ DNS records coletados para {self.target}")
    
    def detect_technologies(self):
        """Detecta tecnologias web"""
        try:
            response = requests.get(f"http://{self.target}", timeout=10)
            
            # Analisar headers
            server = response.headers.get('Server', '')
            powered_by = response.headers.get('X-Powered-By', '')
            
            if server:
                self.results['technologies'].append(f"Server: {server}")
            if powered_by:
                self.results['technologies'].append(f"Powered-By: {powered_by}")
            
            # Analisar conteúdo HTML
            content = response.text.lower()
            
            # Detectar frameworks/CMS comuns
            if 'wordpress' in content:
                self.results['technologies'].append("WordPress")
            if 'drupal' in content:
                self.results['technologies'].append("Drupal")
            if 'joomla' in content:
                self.results['technologies'].append("Joomla")
            
            print(f"✅ Tecnologias detectadas para {self.target}")
            
        except Exception as e:
            print(f"❌ Erro na detecção de tecnologias: {e}")
    
    def generate_report(self):
        """Gera relatório final"""
        report_file = f"osint_report_{self.target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Relatório salvo em: {report_file}")
        
        # Resumo no terminal
        print(f"\n=== RESUMO OSINT - {self.target} ===")
        print(f"Registrar: {self.results['whois'].get('registrar', 'N/A')}")
        print(f"Registros A: {len(self.results['dns'].get('A', []))}")
        print(f"Registros MX: {len(self.results['dns'].get('MX', []))}")
        print(f"Tecnologias: {len(self.results['technologies'])}")
        
        return report_file

def main():
    parser = argparse.ArgumentParser(description='CYPHER OSINT Collector')
    parser.add_argument('target', help='Target domain')
    parser.add_argument('--output', '-o', help='Output file')
    
    args = parser.parse_args()
    
    print(f"🔍 Iniciando coleta OSINT para: {args.target}")
    print("⚖️  Lembre-se: Use apenas para fins éticos e legais")
    print("-" * 50)
    
    collector = OSINTCollector(args.target)
    
    # Executar coletas
    collector.collect_whois()
    collector.collect_dns()
    collector.detect_technologies()
    
    # Gerar relatório
    report_file = collector.generate_report()
    
    print(f"\n🎉 Coleta OSINT concluída!")

if __name__ == "__main__":
    main()
```

## Aspectos Éticos e Legais

### ⚖️ Considerações Legais

1. **Fontes Públicas**: Use apenas informações publicamente disponíveis
2. **Termos de Uso**: Respeite os termos de serviço dos sites
3. **Rate Limiting**: Não sobrecarregue servidores
4. **Jurisdição**: Conheça as leis locais

### 🛡️ Boas Práticas

```python
# Exemplo de rate limiting
import time
import random

def respectful_request(url, min_delay=1, max_delay=3):
    """Faz requisições respeitando rate limits"""
    
    # Delay aleatório entre requisições
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)
    
    # Headers respeitosos
    headers = {
        'User-Agent': 'OSINT Research Tool - Educational Purpose',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    }
    
    return requests.get(url, headers=headers, timeout=10)
```

## Conexões com o Currículo CYPHER

### 🔗 Preparação para Próximos Módulos

#### 2.2 Análise de Vulnerabilidades
- Usar informações coletadas para scanning direcionado
- Identificar serviços para análise detalhada

#### 2.3 Engenharia Social
- Informações sobre funcionários para simulações
- Dados da empresa para pretextos convincentes

### 🚀 Aplicação em Fases Avançadas

#### FASE 3 - WEB SECURITY:
- Mapeamento de aplicações web
- Identificação de tecnologias para testes específicos

#### FASE 7 - FERRAMENTAS:
- Integração com ferramentas de pentest
- Automação de reconhecimento

### 📋 Checklist de Progresso

- [ ] Compreende metodologia OSINT
- [ ] Sabe usar ferramentas básicas
- [ ] Consegue automatizar coletas
- [ ] Entende aspectos éticos/legais
- [ ] Integra conhecimentos da FASE 1

## Próximos Passos

### 📚 Próximo Módulo: [2.2 Análise de Vulnerabilidades](/capitulos/06-analise-vulnerabilidades)

No próximo módulo da **FASE 2**, você aplicará as informações coletadas via OSINT para realizar análise direcionada de vulnerabilidades.

---

**📊 Progresso na FASE 2**: 1/3 módulos concluídos  
**⏱️ Tempo estimado de estudo**: 8-10 horas  
**🎯 Nível de dificuldade**: ⭐⭐⭐⭐☆  
**🔗 Módulo ID**: `osint` (CYPHER Database)
