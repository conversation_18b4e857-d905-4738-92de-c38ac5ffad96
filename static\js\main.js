// CYPHER Learning System - Main JavaScript

class CypherApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('CYPHER System Initialized');
            this.initializeIcons();
        });
    }

    initializeComponents() {
        // Initialize collapsible components
        this.initCollapsibles();
        
        // Initialize time updates if on estudos page
        if (window.location.pathname === '/estudos') {
            this.initTimeUpdates();
            this.initConnectionStatus();
        }
    }

    initializeIcons() {
        // Lucide icons will be initialized by the script tag in base.html
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Collapsible functionality
    initCollapsibles() {
        const collapsibleTriggers = document.querySelectorAll('[data-collapsible-trigger]');
        
        collapsibleTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = trigger.getAttribute('data-target');
                const content = document.getElementById(targetId);
                const chevron = trigger.querySelector('[data-chevron]');
                
                if (content) {
                    const isOpen = content.style.display !== 'none';
                    
                    if (isOpen) {
                        content.style.display = 'none';
                        trigger.setAttribute('aria-expanded', 'false');
                        if (chevron) {
                            chevron.setAttribute('data-lucide', 'chevron-right');
                        }
                    } else {
                        content.style.display = 'block';
                        trigger.setAttribute('aria-expanded', 'true');
                        if (chevron) {
                            chevron.setAttribute('data-lucide', 'chevron-down');
                        }
                    }
                    
                    // Reinitialize icons after changing them
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                }
            });
        });
    }

    // Time updates for estudos page
    initTimeUpdates() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            this.updateTime();
            setInterval(() => this.updateTime(), 1000);
        }
    }

    updateTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleTimeString();
        }
    }

    // Connection status simulation
    initConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        const iconElement = document.getElementById('connection-icon');
        
        if (statusElement && iconElement) {
            this.updateConnectionStatus();
            setInterval(() => this.updateConnectionStatus(), 8000);
        }
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        const iconElement = document.getElementById('connection-icon');
        
        if (statusElement && iconElement) {
            const isConnected = Math.random() > 0.5;
            
            if (isConnected) {
                statusElement.textContent = 'CONNECTED';
                statusElement.className = 'text-white font-mono text-sm';
                iconElement.setAttribute('data-lucide', 'wifi');
                iconElement.className = 'w-4 h-4 text-white';
            } else {
                statusElement.textContent = 'RECONNECTING...';
                statusElement.className = 'text-gray-600 font-mono text-sm';
                iconElement.setAttribute('data-lucide', 'wifi-off');
                iconElement.className = 'w-4 h-4 text-gray-600';
            }
            
            // Reinitialize icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    // Button component functionality
    static createButton(text, variant = 'default', onClick = null) {
        const button = document.createElement('button');
        button.textContent = text;
        
        const baseClasses = 'transition-colors font-medium text-sm h-10 px-4 rounded';
        let variantClasses = '';
        
        switch (variant) {
            case 'ghost':
                variantClasses = 'hover:bg-gray-800 hover:text-white';
                break;
            case 'primary':
                variantClasses = 'bg-white text-black hover:bg-gray-200';
                break;
            default:
                variantClasses = 'bg-gray-800 text-white hover:bg-gray-700';
        }
        
        button.className = `${baseClasses} ${variantClasses}`;
        
        if (onClick) {
            button.addEventListener('click', onClick);
        }
        
        return button;
    }

    // Utility functions
    static addClass(element, className) {
        if (element && !element.classList.contains(className)) {
            element.classList.add(className);
        }
    }

    static removeClass(element, className) {
        if (element && element.classList.contains(className)) {
            element.classList.remove(className);
        }
    }

    static toggleClass(element, className) {
        if (element) {
            element.classList.toggle(className);
        }
    }

    // API calls
    static async fetchTime() {
        try {
            const response = await fetch('/api/time');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching time:', error);
            return null;
        }
    }

    static async fetchConnectionStatus() {
        try {
            const response = await fetch('/api/connection-status');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching connection status:', error);
            return null;
        }
    }

    static async fetchStudyContent() {
        try {
            const response = await fetch('/api/study-content');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching study content:', error);
            return null;
        }
    }
}

// Initialize the app
const cypherApp = new CypherApp();

// Export for use in other scripts
window.CypherApp = CypherApp;
