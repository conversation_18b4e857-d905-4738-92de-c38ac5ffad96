# Bash Scripting

> **Módulo**: 1.4 Programação para Segurança  
> **Fase**: FASE 1 - FUNDAMENTOS  
> **Tópico**: Bash Scripting

## Objetivos de Aprendizagem

Ao final deste tópico, você será capaz de:
- [ ] Criar scripts bash básicos
- [ ] Automatizar tarefas administrativas
- [ ] Usar variáveis e estruturas de controle
- [ ] Implementar scripts de hardening

## Conteúdo

*[Conteúdo a ser preenchido]*

## Exercícios Práticos

*[Exercícios a serem definidos]*

## Recursos Adicionais

*[Links e materiais complementares]*

---
**Anterior**: [Python para Segurança](python-seguranca.md) | **Próximo**: [Git e Versionamento](git-versionamento.md)
