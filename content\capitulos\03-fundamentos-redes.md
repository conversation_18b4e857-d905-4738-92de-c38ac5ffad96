# 1.3 Fundamentos de Redes

> **📍 Localização no Currículo CYPHER**
> **FASE 1: FUNDAMENTOS** → **Módulo 1.3: Fundamentos de Redes**
> Duração: 8-12 semanas | Pré-requisitos: [1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca), [1.2 Fundamentos de SO](/capitulos/02-fundamentos-so)

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Compreender o modelo OSI e TCP/IP
- Identificar protocolos de rede essenciais
- Analisar tráfego de rede básico
- Reconhecer vulnerabilidades comuns em redes
- Aplicar conceitos de **segurança de rede** da Tríade CIA
- Preparar-se para **OSINT** e **análise de vulnerabilidades** na FASE 2

## Introdução às Redes

Uma **rede de computadores** é um conjunto de dispositivos interconectados que podem comunicar-se e compartilhar recursos.

### Tipos de Redes
- 🏠 **LAN** (Local Area Network)
- 🏢 **WAN** (Wide Area Network)
- 🌐 **Internet** (Rede global)
- ☁️ **Cloud** (Computação em nuvem)

## Modelo OSI

O modelo OSI (Open Systems Interconnection) divide a comunicação de rede em 7 camadas:

```
┌─────────────────────────────────┐
│ 7. Application  │ HTTP, FTP, SSH │
├─────────────────────────────────┤
│ 6. Presentation │ SSL/TLS, JPEG  │
├─────────────────────────────────┤
│ 5. Session      │ NetBIOS, RPC   │
├─────────────────────────────────┤
│ 4. Transport    │ TCP, UDP       │
├─────────────────────────────────┤
│ 3. Network      │ IP, ICMP       │
├─────────────────────────────────┤
│ 2. Data Link    │ Ethernet, WiFi │
├─────────────────────────────────┤
│ 1. Physical     │ Cabos, Sinais  │
└─────────────────────────────────┘
```

## Modelo TCP/IP

Modelo prático usado na Internet, com 4 camadas:

```
┌─────────────────────────────────┐
│ 4. Application  │ HTTP, FTP, DNS │
├─────────────────────────────────┤
│ 3. Transport    │ TCP, UDP       │
├─────────────────────────────────┤
│ 2. Internet     │ IP, ICMP       │
├─────────────────────────────────┤
│ 1. Network      │ Ethernet, WiFi │
└─────────────────────────────────┘
```

## Protocolos Essenciais

### Camada de Rede (Layer 3)

#### IP (Internet Protocol)
```bash
# IPv4: ***********
# IPv6: 2001:db8::1

# Verificar IP
ip addr show        # Linux
ipconfig           # Windows
```

#### ICMP (Internet Control Message Protocol)
```bash
# Ping - teste de conectividade
ping google.com
ping -c 4 *******

# Traceroute - rastreamento de rota
traceroute google.com    # Linux
tracert google.com       # Windows
```

### Camada de Transporte (Layer 4)

#### TCP (Transmission Control Protocol)
- Confiável, orientado à conexão
- Controle de fluxo e erro
- Usado por HTTP, HTTPS, SSH, FTP

#### UDP (User Datagram Protocol)
- Não confiável, sem conexão
- Mais rápido que TCP
- Usado por DNS, DHCP, SNMP

```bash
# Verificar portas abertas
netstat -tuln        # Linux
netstat -an          # Windows

# Scan de portas com nmap
nmap -sT target.com  # TCP scan
nmap -sU target.com  # UDP scan
```

### Camada de Aplicação (Layer 7)

#### HTTP/HTTPS
```bash
# Requisição HTTP simples
curl -I http://example.com

# HTTPS com certificado
curl -k https://example.com
```

#### DNS (Domain Name System)
```bash
# Resolução de nome
nslookup google.com
dig google.com

# Verificar registros DNS
dig google.com MX
dig google.com TXT
```

#### SSH (Secure Shell)
```bash
# Conexão SSH
ssh <EMAIL>

# SSH com chave
ssh -i private_key <EMAIL>

# Túnel SSH
ssh -L 8080:localhost:80 <EMAIL>
```

## Endereçamento IP

### Classes de IP (IPv4)
```
Classe A: *******    - ***************  (/8)
Classe B: *********  - ***************  (/16)
Classe C: *********  - ***************  (/24)
```

### Redes Privadas (RFC 1918)
```
10.0.0.0/8        (10.0.0.0 - **************)
**********/12     (********** - **************)
***********/16    (*********** - ***************)
```

### Subnetting
```bash
# Exemplo: ***********/24
# Rede: ***********
# Máscara: *************
# Broadcast: *************
# Hosts: *********** - *************

# Calculadora de subnet
ipcalc ***********/24
```

## Análise de Tráfego

### Wireshark
Ferramenta gráfica para captura e análise de pacotes:

```bash
# Filtros básicos
ip.addr == ***********
tcp.port == 80
http.request.method == "GET"
dns.qry.name contains "google"
```

### tcpdump
Ferramenta de linha de comando para captura:

```bash
# Capturar tráfego HTTP
tcpdump -i eth0 port 80

# Salvar em arquivo
tcpdump -i eth0 -w capture.pcap

# Ler arquivo
tcpdump -r capture.pcap

# Filtrar por host
tcpdump host ***********
```

## Segurança de Redes

### Vulnerabilidades Comuns

#### 1. Sniffing
Interceptação de tráfego não criptografado:
```bash
# Exemplo com tcpdump
tcpdump -i eth0 -A port 23  # Telnet em texto claro
```

#### 2. Man-in-the-Middle (MITM)
Interceptação e modificação de comunicações:
```bash
# Exemplo com ettercap
ettercap -T -M arp:remote /***********// /***********00//
```

#### 3. Port Scanning
Descoberta de serviços ativos:
```bash
# Nmap - scan básico
nmap -sS target.com

# Scan agressivo
nmap -A target.com

# Scan de vulnerabilidades
nmap --script vuln target.com
```

#### 4. DoS/DDoS
Ataques de negação de serviço:
```bash
# Exemplo conceitual (NÃO EXECUTE)
# hping3 -S --flood -V target.com
```

### Medidas de Proteção

#### Firewall
```bash
# iptables (Linux)
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -j DROP

# Windows Firewall
netsh advfirewall firewall add rule name="SSH" dir=in action=allow protocol=TCP localport=22
```

#### VPN (Virtual Private Network)
```bash
# OpenVPN
openvpn --config client.ovpn

# WireGuard
wg-quick up wg0
```

#### IDS/IPS
- **Snort**: Sistema de detecção de intrusão
- **Suricata**: IDS/IPS de próxima geração

## Ferramentas de Rede

### Diagnóstico
```bash
# Conectividade
ping target.com
telnet target.com 80

# Rota de rede
traceroute target.com
mtr target.com

# Informações de rede
ss -tuln              # Linux (substituto do netstat)
lsof -i               # Arquivos/portas abertas
```

### Monitoramento
```bash
# Tráfego em tempo real
iftop                 # Por interface
nethogs               # Por processo
nload                 # Gráfico de banda
```

### Scanning e Reconhecimento
```bash
# Nmap - descoberta de hosts
nmap -sn ***********/24

# Masscan - scanner rápido
masscan -p1-65535 ***********/24 --rate=1000

# Zmap - scanner de Internet
zmap -p 80 -o results.txt
```

## Exercícios Práticos

### Exercício 1: Análise de Conectividade
```bash
# 1. Teste conectividade com Google
ping -c 4 *******

# 2. Trace a rota até o destino
traceroute *******

# 3. Resolva o nome DNS
nslookup google.com
```

### Exercício 2: Scan de Rede Local
```bash
# 1. Descubra sua rede
ip route | grep default

# 2. Scan da rede local
nmap -sn ***********/24

# 3. Scan de portas de um host
nmap -sS ***********
```

### Exercício 3: Captura de Tráfego
```bash
# 1. Capture tráfego HTTP
tcpdump -i any port 80 -w http_traffic.pcap

# 2. Gere tráfego
curl http://example.com

# 3. Analise a captura
tcpdump -r http_traffic.pcap -A
```

## Laboratório Prático

### Configuração de Ambiente
1. **VirtualBox/VMware** com múltiplas VMs
2. **Kali Linux** para ferramentas de segurança
3. **Ubuntu Server** como alvo
4. **pfSense** como firewall

### Cenários de Teste
- Configuração de rede interna
- Implementação de firewall
- Teste de conectividade
- Análise de tráfego

## Recursos Adicionais

- 📚 [Cisco Networking Academy](https://www.netacad.com/)
- 🔧 [Wireshark University](https://www.wireshark.org/docs/)
- 🎓 [PacketLife.net](http://packetlife.net/)
- 🛠️ [Nmap Network Scanning](https://nmap.org/book/)

## Conexões com o Currículo CYPHER

### 🔗 Integração com Módulos Anteriores

Este módulo integra conhecimentos de:
- **[1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca)** - Tríade CIA aplicada a redes
- **[1.2 Fundamentos de SO](/capitulos/02-fundamentos-so)** - Comandos de rede em Windows/Linux

### 🚀 Preparação para Fases Avançadas

Os conhecimentos de rede são fundamentais para:

#### FASE 2 - RECONHECIMENTO:
- **2.1 OSINT** - Reconhecimento passivo de infraestrutura
- **2.2 Análise de Vulnerabilidades** - Scanning de rede e portas

#### FASE 3 - WEB SECURITY:
- **3.1 Desenvolvimento Web** - Protocolos HTTP/HTTPS
- **3.2 OWASP Top 10** - Vulnerabilidades de rede em aplicações

#### FASE 4 - INFRAESTRUTURA:
- **4.1 Segurança de Redes** - Hardening avançado de rede
- **4.2/4.3 Segurança Windows/Linux** - Configuração de rede segura

#### FASE 7 - FERRAMENTAS:
- **7.1 Ferramentas de Pentest** - Nmap, Metasploit, ferramentas de rede
- **7.2 SIEM e Detecção** - Monitoramento de tráfego de rede

#### FASE 8 - ESPECIALIZAÇÃO:
- **8.2 Digital Forensics** - Análise forense de rede

### 🛠️ Ferramentas Essenciais

Ferramentas que você usará em fases avançadas:

#### Análise de Tráfego:
- **Wireshark** (usado em FASE 2, 4, 7, 8)
- **tcpdump** (essencial para FASE 4 e 8)

#### Scanning e Reconhecimento:
- **Nmap** (fundamental para FASE 2 e 7)
- **Masscan** (FASE 7 - Ferramentas)

#### Monitoramento:
- **Snort/Suricata** (FASE 7 - SIEM)
- **pfSense** (FASE 4 - Infraestrutura)

### 📋 Checklist de Progresso

Antes de finalizar a FASE 1, certifique-se de que você:

- [ ] Domina os modelos OSI e TCP/IP
- [ ] Sabe usar Wireshark para análise básica
- [ ] Compreende conceitos de firewall
- [ ] Consegue fazer scanning básico com Nmap
- [ ] Entende vulnerabilidades de rede
- [ ] Completou todos os laboratórios práticos

## Próximos Passos

### 📚 Próximo Módulo: [1.4 Programação para Segurança](/capitulos/04-programacao-seguranca)

No último módulo da **FASE 1**, exploraremos **Programação para Segurança**, aprendendo a desenvolver scripts para automação de tarefas de segurança e preparando-se para as ferramentas avançadas das próximas fases.

### 🎯 Objetivos do Próximo Módulo:
- Python para automação de segurança
- Bash scripting para administração
- Git para versionamento de código
- Preparação para desenvolvimento de exploits (FASE 8)

### 🚀 Após Completar a FASE 1:

Você estará preparado para a **FASE 2: RECONHECIMENTO**, onde aplicará todos os conhecimentos fundamentais em:
- Técnicas de OSINT
- Scanning avançado de vulnerabilidades
- Engenharia social ética

---

**📊 Progresso na FASE 1**: 3/4 módulos concluídos
**⏱️ Tempo estimado de estudo**: 6-8 horas
**🎯 Nível de dificuldade**: ⭐⭐⭐⭐☆
**🔗 Módulo ID**: `network-fundamentals` (CYPHER Database)
