/* CYPHER - Cyberpunk Effects CSS */

/* Neon Glow Effects */
.neon-text {
    text-shadow: 
        0 0 5px var(--primary),
        0 0 10px var(--primary),
        0 0 15px var(--primary),
        0 0 20px var(--primary);
    animation: neon-flicker 2s infinite alternate;
}

@keyframes neon-flicker {
    0%, 100% {
        text-shadow: 
            0 0 5px var(--primary),
            0 0 10px var(--primary),
            0 0 15px var(--primary),
            0 0 20px var(--primary);
    }
    50% {
        text-shadow: 
            0 0 2px var(--primary),
            0 0 5px var(--primary),
            0 0 8px var(--primary),
            0 0 12px var(--primary);
    }
}

/* Holographic Effect */
.holographic {
    background: linear-gradient(
        45deg,
        var(--primary),
        var(--accent),
        var(--primary),
        var(--accent)
    );
    background-size: 400% 400%;
    animation: holographic-shift 3s ease-in-out infinite;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

@keyframes holographic-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Glitch Effect */
.glitch {
    position: relative;
    animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch::before {
    animation: glitch-anim-1 0.5s infinite linear alternate-reverse;
    color: var(--accent);
    z-index: -1;
}

.glitch::after {
    animation: glitch-anim-2 0.5s infinite linear alternate-reverse;
    color: var(--destructive);
    z-index: -2;
}

@keyframes glitch-anim-1 {
    0% { clip: rect(42px, 9999px, 44px, 0); }
    5% { clip: rect(12px, 9999px, 59px, 0); }
    10% { clip: rect(48px, 9999px, 29px, 0); }
    15% { clip: rect(42px, 9999px, 73px, 0); }
    20% { clip: rect(63px, 9999px, 27px, 0); }
    25% { clip: rect(34px, 9999px, 55px, 0); }
    30% { clip: rect(86px, 9999px, 73px, 0); }
    35% { clip: rect(20px, 9999px, 20px, 0); }
    40% { clip: rect(26px, 9999px, 60px, 0); }
    45% { clip: rect(25px, 9999px, 66px, 0); }
    50% { clip: rect(57px, 9999px, 98px, 0); }
    55% { clip: rect(5px, 9999px, 46px, 0); }
    60% { clip: rect(82px, 9999px, 31px, 0); }
    65% { clip: rect(54px, 9999px, 27px, 0); }
    70% { clip: rect(28px, 9999px, 99px, 0); }
    75% { clip: rect(45px, 9999px, 69px, 0); }
    80% { clip: rect(23px, 9999px, 85px, 0); }
    85% { clip: rect(54px, 9999px, 84px, 0); }
    90% { clip: rect(45px, 9999px, 47px, 0); }
    95% { clip: rect(37px, 9999px, 20px, 0); }
    100% { clip: rect(4px, 9999px, 91px, 0); }
}

@keyframes glitch-anim-2 {
    0% { clip: rect(65px, 9999px, 100px, 0); }
    5% { clip: rect(52px, 9999px, 74px, 0); }
    10% { clip: rect(79px, 9999px, 85px, 0); }
    15% { clip: rect(75px, 9999px, 5px, 0); }
    20% { clip: rect(67px, 9999px, 61px, 0); }
    25% { clip: rect(14px, 9999px, 79px, 0); }
    30% { clip: rect(1px, 9999px, 66px, 0); }
    35% { clip: rect(86px, 9999px, 30px, 0); }
    40% { clip: rect(23px, 9999px, 98px, 0); }
    45% { clip: rect(85px, 9999px, 72px, 0); }
    50% { clip: rect(71px, 9999px, 75px, 0); }
    55% { clip: rect(2px, 9999px, 48px, 0); }
    60% { clip: rect(30px, 9999px, 16px, 0); }
    65% { clip: rect(59px, 9999px, 50px, 0); }
    70% { clip: rect(41px, 9999px, 62px, 0); }
    75% { clip: rect(2px, 9999px, 82px, 0); }
    80% { clip: rect(47px, 9999px, 73px, 0); }
    85% { clip: rect(3px, 9999px, 27px, 0); }
    90% { clip: rect(26px, 9999px, 55px, 0); }
    95% { clip: rect(40px, 9999px, 69px, 0); }
    100% { clip: rect(88px, 9999px, 70px, 0); }
}

@keyframes glitch-skew {
    2% { transform: skew(0.5deg); }
    4% { transform: skew(-0.8deg); }
    6% { transform: skew(0.2deg); }
    8% { transform: skew(-0.1deg); }
    10% { transform: skew(0.4deg); }
    12% { transform: skew(-0.7deg); }
    14% { transform: skew(0deg); }
}

/* Scan Lines Effect */
.scan-lines {
    position: relative;
    overflow: hidden;
}

.scan-lines::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 136, 0.03) 2px,
        rgba(0, 255, 136, 0.03) 4px
    );
    pointer-events: none;
    z-index: 1;
}

/* Circuit Board Pattern */
.circuit-bg {
    background-image: 
        radial-gradient(circle at 25% 25%, var(--primary) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--accent) 2px, transparent 2px),
        linear-gradient(var(--border) 1px, transparent 1px),
        linear-gradient(90deg, var(--border) 1px, transparent 1px);
    background-size: 50px 50px, 50px 50px, 25px 25px, 25px 25px;
    background-position: 0 0, 25px 25px, 0 0, 0 0;
    opacity: 0.1;
}

/* Data Stream Effect */
.data-stream {
    position: relative;
    overflow: hidden;
}

.data-stream::after {
    content: '01001000 01100101 01101100 01101100 01101111 00100000 01010111 01101111 01110010 01101100 01100100';
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: var(--font-geist-mono);
    font-size: 0.5rem;
    line-height: 1;
    white-space: nowrap;
    animation: data-flow 10s linear infinite;
    opacity: 0.3;
    z-index: -1;
}

@keyframes data-flow {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Pulse Effect */
.pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 5px var(--primary-glow);
    }
    to {
        box-shadow: 0 0 20px var(--primary-glow), 0 0 30px var(--primary-glow);
    }
}

/* Matrix Rain Effect */
.matrix-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.matrix-rain::before {
    content: '0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1';
    position: absolute;
    top: -100%;
    left: 0;
    width: 100%;
    height: 200%;
    color: var(--primary);
    font-family: var(--font-geist-mono);
    font-size: 0.8rem;
    line-height: 1.2;
    opacity: 0.1;
    animation: matrix-fall 15s linear infinite;
    white-space: pre-wrap;
    word-break: break-all;
}

@keyframes matrix-fall {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100vh); }
}

/* Utility Classes */
.cyber-border {
    border: 1px solid var(--border);
    position: relative;
}

.cyber-border::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: var(--gradient-primary);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cyber-border:hover::before {
    opacity: 1;
}

/* Enhanced Animations for Estudos Page */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes countUp {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

.animate-count-up {
    animation: countUp 0.8s ease-out forwards;
}

/* Enhanced Card Hover Effects */
.cyber-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cyber-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 20px var(--primary-glow);
}

/* Progress Bar Enhancements */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Status Indicators */
.status-indicator {
    position: relative;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    transform: translateY(-50%);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-50%) scale(1.2);
    }
}

/* Interactive Elements */
.interactive-hover {
    transition: all 0.2s ease;
}

.interactive-hover:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(
        90deg,
        var(--muted) 25%,
        var(--border) 50%,
        var(--muted) 75%
    );
    background-size: 200% 100%;
    animation: loading-skeleton 1.5s infinite;
}

@keyframes loading-skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
