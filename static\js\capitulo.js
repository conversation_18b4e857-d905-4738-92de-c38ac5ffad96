// CYPHER Capítulo - JavaScript

class CapituloPage {
    constructor() {
        this.currentTheme = localStorage.getItem('cypher-theme') || 'dark';
        this.tocVisible = true;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.generateTableOfContents();
        this.setupScrollToTop();
        this.setupKeyboardShortcuts();
        this.loadBookmarkStatus();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // TOC toggle
        const tocToggle = document.getElementById('toc-toggle');
        if (tocToggle) {
            tocToggle.addEventListener('click', () => this.toggleTOC());
        }

        // Scroll to top
        const scrollToTop = document.getElementById('scroll-to-top');
        if (scrollToTop) {
            scrollToTop.addEventListener('click', () => this.scrollToTop());
        }

        // Window scroll for showing/hiding scroll button
        window.addEventListener('scroll', () => this.handleScroll());
    }

    setupTheme() {
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
        localStorage.setItem('cypher-theme', this.currentTheme);
    }

    applyTheme(theme) {
        const html = document.documentElement;
        if (theme === 'light') {
            html.classList.remove('dark');
            html.classList.add('light');
            document.body.style.backgroundColor = '#ffffff';
            document.body.style.color = '#111827';
        } else {
            html.classList.remove('light');
            html.classList.add('dark');
            document.body.style.backgroundColor = '#000000';
            document.body.style.color = '#ffffff';
        }
    }

    updateThemeIcon() {
        const themeToggle = document.getElementById('theme-toggle');
        const icon = themeToggle?.querySelector('i');
        if (icon) {
            icon.setAttribute('data-lucide', this.currentTheme === 'dark' ? 'sun' : 'moon');
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    generateTableOfContents() {
        const tocContainer = document.getElementById('table-of-contents');
        const headings = document.querySelectorAll('.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4');
        
        if (!tocContainer || headings.length === 0) return;

        let tocHTML = '';
        headings.forEach((heading, index) => {
            const level = parseInt(heading.tagName.charAt(1));
            const id = `heading-${index}`;
            const text = heading.textContent;
            
            // Add ID to heading for linking
            heading.id = id;
            
            // Create TOC entry
            const indent = (level - 1) * 1; // 1rem per level
            tocHTML += `
                <a href="#${id}" 
                   class="block py-1 px-2 text-sm text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors toc-link"
                   style="margin-left: ${indent}rem;"
                   data-level="${level}">
                    ${text}
                </a>
            `;
        });

        tocContainer.innerHTML = tocHTML;

        // Setup TOC click handlers
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    this.updateActiveTOCItem(link);
                }
            });
        });

        // Setup intersection observer for active TOC highlighting
        this.setupTOCHighlighting(headings);
    }

    setupTOCHighlighting(headings) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.id;
                    const tocLink = document.querySelector(`a[href="#${id}"]`);
                    if (tocLink) {
                        this.updateActiveTOCItem(tocLink);
                    }
                }
            });
        }, {
            rootMargin: '-20% 0px -70% 0px'
        });

        headings.forEach(heading => observer.observe(heading));
    }

    updateActiveTOCItem(activeLink) {
        // Remove active class from all TOC links
        document.querySelectorAll('.toc-link').forEach(link => {
            link.classList.remove('bg-gray-800', 'text-white');
            link.classList.add('text-gray-300');
        });

        // Add active class to current link
        activeLink.classList.remove('text-gray-300');
        activeLink.classList.add('bg-gray-800', 'text-white');
    }

    toggleTOC() {
        const tocSidebar = document.getElementById('toc-sidebar');
        const tocToggle = document.getElementById('toc-toggle');
        
        if (tocSidebar) {
            this.tocVisible = !this.tocVisible;
            
            if (this.tocVisible) {
                tocSidebar.style.display = 'block';
                tocToggle.classList.remove('bg-gray-800');
            } else {
                tocSidebar.style.display = 'none';
                tocToggle.classList.add('bg-gray-800');
            }
        }
    }

    setupScrollToTop() {
        const scrollButton = document.getElementById('scroll-to-top');
        if (scrollButton) {
            scrollButton.style.display = 'none';
        }
    }

    handleScroll() {
        const scrollButton = document.getElementById('scroll-to-top');
        if (scrollButton) {
            if (window.scrollY > 300) {
                scrollButton.style.display = 'block';
            } else {
                scrollButton.style.display = 'none';
            }
        }
    }

    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search (redirect to chapters page)
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                window.location.href = '/capitulos';
            }

            // T for toggle TOC
            if (e.key === 't' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    this.toggleTOC();
                }
            }

            // B for bookmark
            if (e.key === 'b' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    toggleBookmark();
                }
            }
        });
    }

    loadBookmarkStatus() {
        const chapterId = this.getCurrentChapterId();
        const bookmarks = JSON.parse(localStorage.getItem('cypher-bookmarks') || '[]');
        const isBookmarked = bookmarks.includes(chapterId);
        
        this.updateBookmarkIcon(isBookmarked);
    }

    getCurrentChapterId() {
        const path = window.location.pathname;
        return path.split('/').pop();
    }

    updateBookmarkIcon(isBookmarked) {
        const bookmarkButtons = document.querySelectorAll('[onclick="toggleBookmark()"]');
        bookmarkButtons.forEach(button => {
            const icon = button.querySelector('i');
            if (icon) {
                icon.setAttribute('data-lucide', isBookmarked ? 'bookmark' : 'bookmark-plus');
                if (isBookmarked) {
                    button.classList.add('text-yellow-400');
                } else {
                    button.classList.remove('text-yellow-400');
                }
            }
        });

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
}

// Global functions
function toggleBookmark() {
    const chapterId = window.location.pathname.split('/').pop();
    const bookmarks = JSON.parse(localStorage.getItem('cypher-bookmarks') || '[]');
    
    const index = bookmarks.indexOf(chapterId);
    if (index > -1) {
        bookmarks.splice(index, 1);
        console.log('Capítulo removido dos favoritos');
    } else {
        bookmarks.push(chapterId);
        console.log('Capítulo adicionado aos favoritos');
    }
    
    localStorage.setItem('cypher-bookmarks', JSON.stringify(bookmarks));
    
    // Update bookmark icon
    const page = window.cypherCapituloPage;
    if (page) {
        page.updateBookmarkIcon(bookmarks.includes(chapterId));
    }
}

function shareChapter() {
    const title = document.title;
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            alert('Link copiado para a área de transferência!');
        }).catch(() => {
            alert(`Link do capítulo: ${url}`);
        });
    }
}

function printChapter() {
    window.print();
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    window.cypherCapituloPage = new CapituloPage();
});
