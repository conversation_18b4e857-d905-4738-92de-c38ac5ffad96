"use client"

import { useState } from "react"
import {
  ChevronDown,
  ChevronRight,
  Terminal,
  Code,
  Server,
  Network,
  Skull,
  Eye,
  Zap,
  Shield,
  Globe,
  Smartphone,
  Wrench,
  Target,
  Award,
  Cloud,
  Search,
  Users,
  Lock,
  Bug,
  Microscope,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

const studyContent = [
  {
    id: "fase1",
    title: "[FASE 1: FUNDAMENTOS]",
    icon: Shield,
    duration: "8-12 semanas",
    items: [
      {
        id: "intro-security",
        title: "1.1 Introdução à Segurança",
        icon: Lock,
        topics: ["Conceitos Fundamentais", "Tríade CIA", "Tipos de Ataques", "Frameworks de Segurança"],
      },
      {
        id: "os-fundamentals",
        title: "1.2 Fundamentos de SO",
        icon: Terminal,
        topics: ["Windows Fundamentals", "Linux Fundamentals", "Windows Avançado", "Linux Avançado"],
      },
      {
        id: "network-fundamentals",
        title: "1.3 Fundamentos de Redes",
        icon: Network,
        topics: ["Conceitos de Rede", "Protocolos Essenciais", "Análise de Tráfego", "Segurança de Rede"],
      },
      {
        id: "programming",
        title: "1.4 Programação para Segurança",
        icon: Code,
        topics: ["Python Fundamentals", "Python para Segurança", "Bash Scripting", "Git e Versionamento"],
      },
    ],
  },
  {
    id: "fase2",
    title: "[FASE 2: RECONHECIMENTO]",
    icon: Search,
    duration: "10-14 semanas",
    items: [
      {
        id: "osint",
        title: "2.1 OSINT",
        icon: Eye,
        topics: ["Introdução ao OSINT", "Ferramentas Básicas", "OSINT Avançado", "OSINT Profissional"],
      },
      {
        id: "vulnerability-analysis",
        title: "2.2 Análise de Vulnerabilidades",
        icon: Bug,
        topics: [
          "Conceitos de Vulnerabilidade",
          "Ferramentas de Scanning",
          "Scanning Avançado",
          "Vulnerability Management",
        ],
      },
      {
        id: "social-engineering",
        title: "2.3 Engenharia Social",
        icon: Users,
        topics: ["Psicologia da Segurança", "Tipos de Ataques", "Simulações Éticas", "Awareness e Treinamento"],
      },
    ],
  },
  {
    id: "fase3",
    title: "[FASE 3: WEB SECURITY]",
    icon: Globe,
    duration: "14-18 semanas",
    items: [
      {
        id: "web-dev",
        title: "3.1 Desenvolvimento Web",
        icon: Code,
        topics: ["Tecnologias Web", "Arquitetura Web", "Desenvolvimento Backend"],
      },
      {
        id: "owasp-top10",
        title: "3.2 OWASP Top 10",
        icon: Shield,
        topics: [
          "OWASP Top 10 - Parte 1",
          "Ferramentas Básicas",
          "OWASP Top 10 - Parte 2",
          "Vulnerabilidades Avançadas",
        ],
      },
      {
        id: "apis-modern",
        title: "3.3 APIs e Apps Modernas",
        icon: Network,
        topics: ["API Security", "Tecnologias Modernas"],
      },
    ],
  },
  {
    id: "fase4",
    title: "[FASE 4: INFRAESTRUTURA]",
    icon: Server,
    duration: "16-20 semanas",
    items: [
      {
        id: "network-security",
        title: "4.1 Segurança de Redes",
        icon: Network,
        topics: ["Hardening de Rede", "Protocolos Seguros", "Ataques de Rede", "Técnicas Avançadas"],
      },
      {
        id: "windows-security",
        title: "4.2 Segurança Windows",
        icon: Terminal,
        topics: ["Hardening Windows", "Active Directory Basics", "Windows Security", "Advanced Windows Attacks"],
      },
      {
        id: "linux-security",
        title: "4.3 Segurança Linux",
        icon: Terminal,
        topics: ["Linux Hardening", "Monitoring e Logs", "Linux Security", "Advanced Linux Security"],
      },
    ],
  },
  {
    id: "fase5",
    title: "[FASE 5: CLOUD SECURITY]",
    icon: Cloud,
    duration: "12-16 semanas",
    items: [
      {
        id: "cloud-fundamentals",
        title: "5.1 Cloud Computing",
        icon: Cloud,
        topics: ["Cloud Basics", "Cloud Security Fundamentals", "AWS Security", "Azure Security"],
      },
      {
        id: "container-k8s",
        title: "5.2 Container & Kubernetes",
        icon: Server,
        topics: ["Container Basics", "Container Security", "Kubernetes Security"],
      },
    ],
  },
  {
    id: "fase6",
    title: "[FASE 6: MOBILE SECURITY]",
    icon: Smartphone,
    duration: "10-14 semanas",
    items: [
      {
        id: "android-security",
        title: "6.1 Android Security",
        icon: Smartphone,
        topics: ["Android Fundamentals", "Static Analysis", "Dynamic Analysis"],
      },
      {
        id: "ios-security",
        title: "6.2 iOS Security",
        icon: Smartphone,
        topics: ["iOS Fundamentals"],
      },
    ],
  },
  {
    id: "fase7",
    title: "[FASE 7: FERRAMENTAS]",
    icon: Wrench,
    duration: "8-12 semanas",
    items: [
      {
        id: "pentest-tools",
        title: "7.1 Ferramentas de Pentest",
        icon: Target,
        topics: ["Kali Linux", "Metasploit Framework", "Automação"],
      },
      {
        id: "siem-detection",
        title: "7.2 SIEM e Detecção",
        icon: Eye,
        topics: ["SIEM Fundamentals", "Threat Hunting"],
      },
    ],
  },
  {
    id: "fase8",
    title: "[FASE 8: ESPECIALIZAÇÃO]",
    icon: Microscope,
    duration: "16-24 semanas",
    items: [
      {
        id: "malware-analysis",
        title: "8.1 Malware Analysis",
        icon: Bug,
        topics: ["Static Analysis", "Dynamic Analysis", "Reverse Engineering"],
      },
      {
        id: "digital-forensics",
        title: "8.2 Digital Forensics",
        icon: Search,
        topics: ["Forensics Fundamentals", "Network Forensics"],
      },
      {
        id: "exploit-dev",
        title: "8.3 Exploit Development",
        icon: Code,
        topics: ["Assembly Language", "Buffer Overflows"],
      },
    ],
  },
  {
    id: "fase9",
    title: "[FASE 9: CERTIFICAÇÕES]",
    icon: Award,
    duration: "8-12 semanas",
    items: [
      {
        id: "certifications",
        title: "9.1 Certificações Essenciais",
        icon: Award,
        topics: ["CompTIA Security+", "CEH (Certified Ethical Hacker)", "CISSP", "OSCP"],
      },
      {
        id: "career-dev",
        title: "9.2 Desenvolvimento de Carreira",
        icon: Target,
        topics: ["Soft Skills", "Portfolio Development"],
      },
    ],
  },
]

export function StudySidebar() {
  const [openSections, setOpenSections] = useState<string[]>([])
  const [openSubSections, setOpenSubSections] = useState<string[]>([])

  const toggleSection = (sectionId: string) => {
    setOpenSections((prev) => (prev.includes(sectionId) ? prev.filter((id) => id !== sectionId) : [...prev, sectionId]))
  }

  const toggleSubSection = (subSectionId: string) => {
    setOpenSubSections((prev) =>
      prev.includes(subSectionId) ? prev.filter((id) => id !== subSectionId) : [...prev, subSectionId],
    )
  }

  return (
    <aside className="w-80 border-r border-gray-800 bg-black h-screen overflow-y-auto relative">
      <div className="p-4 border-b border-gray-800 relative z-10">
        <h2 className="text-lg font-bold text-white">CYPHER_MODULES</h2>
        <p className="text-xs text-gray-500 font-mono mt-1">root@cypher:/learning#</p>
        <div className="mt-2 flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full"></div>
          <span className="text-xs text-gray-400 font-mono">NEURAL_LINK_ACTIVE</span>
        </div>
      </div>

      <nav className="p-2 relative z-10">
        {studyContent.map((phase) => {
          const PhaseIcon = phase.icon
          const isPhaseOpen = openSections.includes(phase.id)

          return (
            <Collapsible key={phase.id} open={isPhaseOpen} onOpenChange={() => toggleSection(phase.id)}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-start p-3 h-auto font-mono text-left hover:bg-gray-800 hover:text-white transition-all mb-1"
                >
                  <div className="flex items-center gap-3 w-full">
                    <PhaseIcon className="w-4 h-4 text-white shrink-0" />
                    <div className="flex-1">
                      <div className="text-sm font-bold">{phase.title}</div>
                      <div className="text-xs text-gray-500">{phase.duration}</div>
                    </div>
                    {isPhaseOpen ? (
                      <ChevronDown className="w-4 h-4 text-gray-500" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-500" />
                    )}
                  </div>
                </Button>
              </CollapsibleTrigger>

              <CollapsibleContent className="ml-4 border-l border-gray-800">
                {phase.items.map((module) => {
                  const ModuleIcon = module.icon
                  const isModuleOpen = openSubSections.includes(module.id)

                  return (
                    <Collapsible key={module.id} open={isModuleOpen} onOpenChange={() => toggleSubSection(module.id)}>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          className="w-full justify-start p-2 pl-6 h-auto font-mono text-left text-gray-300 hover:bg-gray-800 hover:text-white transition-all"
                        >
                          <div className="flex items-center gap-2 w-full">
                            <ModuleIcon className="w-3 h-3 text-white shrink-0" />
                            <span className="flex-1 text-xs">{module.title}</span>
                            {isModuleOpen ? (
                              <ChevronDown className="w-3 h-3 text-gray-500" />
                            ) : (
                              <ChevronRight className="w-3 h-3 text-gray-500" />
                            )}
                          </div>
                        </Button>
                      </CollapsibleTrigger>

                      <CollapsibleContent className="ml-6 border-l border-gray-700">
                        {module.topics.map((topic, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            className="w-full justify-start p-2 pl-4 h-auto font-mono text-left text-gray-400 hover:bg-gray-800 hover:text-white transition-all"
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-1 h-1 bg-white rounded-full opacity-60" />
                              <span className="text-xs">{topic}</span>
                            </div>
                          </Button>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  )
                })}
              </CollapsibleContent>
            </Collapsible>
          )
        })}
      </nav>

      {/* Footer da sidebar */}
      <div className="p-4 border-t border-gray-800 mt-auto relative z-10">
        <div className="text-xs font-mono text-gray-500">
          <div className="flex items-center gap-2 mb-2">
            <Skull className="w-3 h-3" />
            <span>LEARNING_MODE: ACTIVE</span>
          </div>
          <div>$ whoami</div>
          <div className="text-white">cyber_student</div>
          <div className="mt-2">$ progress</div>
          <div>phase_1: initializing...</div>
          <div className="mt-2 text-white">
            <Zap className="w-3 h-3 inline mr-1" />
            STATUS: READY_TO_LEARN
          </div>
        </div>
      </div>
    </aside>
  )
}
