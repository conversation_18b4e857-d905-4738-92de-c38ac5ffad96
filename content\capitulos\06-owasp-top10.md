# 3.2 OWASP Top 10 - Vulnerabilidades Web Críticas

> **📍 Localização no Currículo CYPHER**  
> **FASE 3: WEB SECURITY** → **Módulo 3.2: OWASP Top 10**  
> Duração: 14-18 semanas | Pré-requisitos: **FASE 1 e 2** completas, **3.1 Desenvolvimento Web**

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- <PERSON>mpreender as 10 vulnerabilidades web mais críticas
- Identificar e explorar vulnerabilidades OWASP Top 10
- Aplicar técnicas de mitigação e prevenção
- Usar ferramentas especializadas para testes web
- Integrar conhecimentos de **OSINT** e **análise de vulnerabilidades**
- Preparar-se para **APIs e aplicações modernas** (módulo 3.3)

## Introdução ao OWASP Top 10

O **OWASP Top 10** é uma lista das 10 vulnerabilidades de segurança web mais críticas, atualizada regularmente pela comunidade OWASP. É o padrão de referência para segurança de aplicações web.

### Aplicação dos Conhecimentos Anteriores

#### Da FASE 1 - FUNDAMENTOS:
- **[1.1 Tríade CIA](/capitulos/01-introducao-seguranca)**: Cada vulnerabilidade compromete C, I ou A
- **[1.3 Redes](/capitulos/03-fundamentos-redes)**: Protocolos HTTP/HTTPS e análise de tráfego
- **[1.4 Programação](/capitulos/04-programacao-seguranca)**: Scripts para automação de testes

#### Da FASE 2 - RECONHECIMENTO:
- **[2.1 OSINT](/capitulos/05-osint-introducao)**: Reconhecimento de aplicações web
- **2.2 Análise de Vulnerabilidades**: Scanning direcionado de aplicações

## OWASP Top 10 (2021)

### A01:2021 - Broken Access Control

**Descrição**: Falhas no controle de acesso permitem que usuários acessem recursos não autorizados.

#### Exemplos Comuns:
- Bypass de verificações de autorização
- Manipulação de URLs para acessar páginas restritas
- Elevação de privilégios
- CORS mal configurado

#### Teste Prático:
```python
import requests

def test_broken_access_control(base_url):
    """Testa falhas básicas de controle de acesso"""
    
    # Teste 1: Acesso direto a páginas administrativas
    admin_paths = [
        '/admin',
        '/admin.php',
        '/administrator',
        '/wp-admin',
        '/panel',
        '/dashboard'
    ]
    
    print("🔍 Testando acesso direto a painéis administrativos...")
    for path in admin_paths:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            if response.status_code == 200:
                print(f"⚠️  Possível painel admin exposto: {path}")
            elif response.status_code == 403:
                print(f"🔒 Painel protegido (403): {path}")
        except:
            pass
    
    # Teste 2: Manipulação de parâmetros
    test_urls = [
        f"{base_url}/user/profile?id=1",
        f"{base_url}/user/profile?id=2",
        f"{base_url}/user/profile?id=admin",
        f"{base_url}/api/users/1",
        f"{base_url}/api/users/2"
    ]
    
    print("\n🔍 Testando manipulação de parâmetros...")
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            print(f"Status {response.status_code}: {url}")
        except:
            pass

# Uso (apenas em ambientes de teste!)
# test_broken_access_control("http://testphp.vulnweb.com")
```

#### Mitigação:
- Implementar controle de acesso baseado em roles
- Validar permissões no servidor
- Usar tokens JWT seguros
- Implementar rate limiting

### A02:2021 - Cryptographic Failures

**Descrição**: Falhas relacionadas à criptografia, incluindo dados não criptografados e algoritmos fracos.

#### Exemplos Comuns:
- Transmissão de dados sensíveis em texto claro
- Uso de algoritmos criptográficos fracos
- Chaves criptográficas fracas ou mal gerenciadas
- Certificados SSL/TLS inválidos

#### Teste com Python:
```python
import ssl
import socket
import requests
from urllib3.exceptions import InsecureRequestWarning

def test_crypto_failures(domain):
    """Testa falhas criptográficas básicas"""
    
    print(f"🔍 Testando falhas criptográficas em {domain}")
    
    # Teste 1: Verificar se HTTPS está disponível
    try:
        response = requests.get(f"https://{domain}", timeout=5)
        print("✅ HTTPS disponível")
    except:
        print("❌ HTTPS não disponível ou com problemas")
    
    # Teste 2: Verificar redirecionamento HTTP -> HTTPS
    try:
        response = requests.get(f"http://{domain}", timeout=5, allow_redirects=False)
        if response.status_code in [301, 302] and 'https' in response.headers.get('Location', ''):
            print("✅ Redirecionamento HTTP -> HTTPS configurado")
        else:
            print("⚠️  Redirecionamento HTTP -> HTTPS não configurado")
    except:
        pass
    
    # Teste 3: Verificar certificado SSL
    try:
        context = ssl.create_default_context()
        with socket.create_connection((domain, 443), timeout=5) as sock:
            with context.wrap_socket(sock, server_hostname=domain) as ssock:
                cert = ssock.getpeercert()
                print(f"✅ Certificado SSL válido - Emissor: {cert['issuer']}")
    except ssl.SSLError as e:
        print(f"❌ Problema no certificado SSL: {e}")
    except:
        print("❌ Não foi possível verificar o certificado SSL")

# Uso
# test_crypto_failures("exemplo.com")
```

### A03:2021 - Injection

**Descrição**: Falhas de injeção ocorrem quando dados não confiáveis são enviados como parte de um comando ou consulta.

#### Tipos Principais:
- **SQL Injection**
- **NoSQL Injection**
- **Command Injection**
- **LDAP Injection**

#### SQL Injection - Teste Manual:
```python
import requests

def test_sql_injection(url, param):
    """Testa SQL injection básico"""
    
    # Payloads básicos para teste
    payloads = [
        "'",
        "' OR '1'='1",
        "' OR '1'='1' --",
        "' UNION SELECT NULL--",
        "'; DROP TABLE users; --"
    ]
    
    print(f"🔍 Testando SQL Injection em {url}")
    
    for payload in payloads:
        try:
            # Teste GET
            response = requests.get(f"{url}?{param}={payload}", timeout=5)
            
            # Indicadores de SQL injection
            error_indicators = [
                "sql syntax",
                "mysql_fetch",
                "ora-01756",
                "microsoft jet database",
                "sqlite_master"
            ]
            
            for indicator in error_indicators:
                if indicator.lower() in response.text.lower():
                    print(f"⚠️  Possível SQL Injection detectado com payload: {payload}")
                    break
                    
        except:
            pass

# IMPORTANTE: Use apenas em ambientes de teste autorizados!
```

#### Mitigação:
- Usar prepared statements/parameterized queries
- Validar e sanitizar todas as entradas
- Implementar WAF (Web Application Firewall)
- Princípio do menor privilégio para banco de dados

### A04:2021 - Insecure Design

**Descrição**: Falhas de design que não podem ser corrigidas apenas com implementação.

#### Exemplos:
- Falta de rate limiting
- Ausência de validação de entrada
- Fluxos de negócio inseguros
- Arquitetura sem defesa em profundidade

### A05:2021 - Security Misconfiguration

**Descrição**: Configurações de segurança inadequadas ou padrões inseguros.

#### Teste Automatizado:
```python
import requests

def test_security_misconfiguration(base_url):
    """Testa configurações de segurança comuns"""
    
    # Teste 1: Arquivos/diretórios sensíveis expostos
    sensitive_paths = [
        '/.git',
        '/.env',
        '/config.php',
        '/phpinfo.php',
        '/server-status',
        '/server-info',
        '/robots.txt',
        '/sitemap.xml',
        '/.htaccess',
        '/web.config'
    ]
    
    print("🔍 Testando exposição de arquivos sensíveis...")
    for path in sensitive_paths:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            if response.status_code == 200:
                print(f"⚠️  Arquivo sensível exposto: {path}")
        except:
            pass
    
    # Teste 2: Headers de segurança
    try:
        response = requests.get(base_url, timeout=5)
        headers = response.headers
        
        security_headers = {
            'X-Frame-Options': 'Proteção contra clickjacking',
            'X-Content-Type-Options': 'Prevenção de MIME sniffing',
            'X-XSS-Protection': 'Proteção XSS básica',
            'Strict-Transport-Security': 'HSTS',
            'Content-Security-Policy': 'CSP'
        }
        
        print("\n🔍 Verificando headers de segurança...")
        for header, description in security_headers.items():
            if header in headers:
                print(f"✅ {header}: {description}")
            else:
                print(f"❌ {header} ausente: {description}")
                
    except:
        print("❌ Erro ao verificar headers")

# Uso
# test_security_misconfiguration("https://exemplo.com")
```

## Ferramentas Especializadas

### 1. Burp Suite Community

```bash
# Download do site oficial
# https://portswigger.net/burp/communitydownload

# Configuração básica:
# 1. Configurar proxy (127.0.0.1:8080)
# 2. Instalar certificado CA
# 3. Configurar browser para usar proxy
```

### 2. OWASP ZAP

```bash
# Instalar
sudo apt install zaproxy

# Usar via linha de comando
zap-baseline.py -t https://exemplo.com

# Scan completo
zap-full-scan.py -t https://exemplo.com
```

### 3. SQLMap

```bash
# Instalar
git clone https://github.com/sqlmapproject/sqlmap.git

# Teste básico
python sqlmap.py -u "http://exemplo.com/page?id=1" --dbs

# Teste com cookie
python sqlmap.py -u "http://exemplo.com/page" --cookie="PHPSESSID=abc123" --dbs
```

### 4. Nikto

```bash
# Instalar
sudo apt install nikto

# Scan básico
nikto -h https://exemplo.com

# Scan com tuning específico
nikto -h https://exemplo.com -Tuning 1,2,3,4,5
```

## Laboratório Prático

### Ambiente de Teste Recomendado

1. **DVWA (Damn Vulnerable Web Application)**
```bash
# Docker
docker run --rm -it -p 80:80 vulnerables/web-dvwa

# Acesso: http://localhost
# Login: admin/password
```

2. **WebGoat**
```bash
# Docker
docker run -p 8080:8080 -t webgoat/webgoat-8.0

# Acesso: http://localhost:8080/WebGoat
```

3. **Mutillidae II**
```bash
# Docker
docker run -p 80:80 citizenstig/nowasp

# Acesso: http://localhost
```

## Conexões com o Currículo CYPHER

### 🔗 Integração com Módulos Anteriores

#### FASE 2 - RECONHECIMENTO:
- **OSINT**: Identificação de tecnologias web para testes direcionados
- **Análise de Vulnerabilidades**: Scanning de aplicações web

#### FASE 1 - FUNDAMENTOS:
- **Programação**: Automação de testes de segurança
- **Redes**: Análise de tráfego HTTP/HTTPS

### 🚀 Preparação para Módulos Avançados

#### 3.3 APIs e Apps Modernas:
- Aplicação dos conceitos OWASP em APIs REST
- Testes de segurança em aplicações SPA

#### FASE 7 - FERRAMENTAS:
- Integração com ferramentas de pentest
- Automação de testes OWASP Top 10

#### FASE 8 - ESPECIALIZAÇÃO:
- Desenvolvimento de exploits web
- Análise forense de aplicações web

### 📋 Checklist de Progresso

- [ ] Compreende todas as 10 vulnerabilidades OWASP
- [ ] Sabe identificar e explorar vulnerabilidades básicas
- [ ] Domina ferramentas como Burp Suite e ZAP
- [ ] Consegue automatizar testes com Python
- [ ] Entende técnicas de mitigação
- [ ] Praticou em laboratórios controlados

## Próximos Passos

### 📚 Próximo Módulo: [3.3 APIs e Apps Modernas](/capitulos/07-apis-modernas)

No próximo módulo da **FASE 3**, você aplicará os conhecimentos do OWASP Top 10 em APIs REST, GraphQL e aplicações modernas (SPA, PWA).

### 🎯 Objetivos do Próximo Módulo:
- Segurança de APIs REST e GraphQL
- Testes de aplicações Single Page (SPA)
- JWT e OAuth 2.0 security
- Containerização e segurança

---

**📊 Progresso na FASE 3**: 2/3 módulos concluídos  
**⏱️ Tempo estimado de estudo**: 12-16 horas  
**🎯 Nível de dificuldade**: ⭐⭐⭐⭐⭐  
**🔗 Módulo ID**: `owasp-top10` (CYPHER Database)  
**🏆 Conquista**: "Web Security Expert" 🌐
