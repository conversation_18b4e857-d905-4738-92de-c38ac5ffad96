/* CSS Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables - <PERSON><PERSON> Dark */
:root {
    --radius: 0.625rem;
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    
    /* Fonts */
    --font-geist-sans: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-geist-mono: 'Geist Mono', 'Fira Code', 'Consolas', monospace;
}

/* Base Styles */
html {
    font-family: var(--font-geist-sans);
    line-height: 1.5;
}

body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: var(--font-geist-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
.font-mono {
    font-family: var(--font-geist-mono);
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

/* Text Sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Colors */
.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }

.bg-black { background-color: #000000; }
.bg-white { background-color: #ffffff; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

/* Layout */
.min-h-screen { min-height: 100vh; }
.h-screen { height: 100vh; }
.w-full { width: 100%; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Flexbox */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-items-center { justify-items: center; }
.flex-1 { flex: 1 1 0%; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.row-start-2 { grid-row-start: 2; }
.row-start-3 { grid-row-start: 3; }

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.pl-4 { padding-left: 1rem; }
.pl-6 { padding-left: 1.5rem; }
.pb-20 { padding-bottom: 5rem; }

.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-auto { margin-top: auto; }
.ml-4 { margin-left: 1rem; }
.ml-6 { margin-left: 1.5rem; }
.mr-1 { margin-right: 0.25rem; }

/* Gaps */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-16 { gap: 4rem; }

/* Borders */
.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-r { border-right-width: 1px; }
.border-l { border-left-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-solid { border-style: solid; }
.border-transparent { border-color: transparent; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-full { border-radius: 9999px; }

/* Sizing */
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-6 { width: 1.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-80 { width: 20rem; }
.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-auto { height: auto; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.top-2 { top: 0.5rem; }
.right-2 { right: 0.5rem; }
.z-10 { z-index: 10; }

/* Overflow */
.overflow-y-auto { overflow-y: auto; }

/* Opacity */
.opacity-60 { opacity: 0.6; }

/* Transitions */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }

/* Hover States */
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:bg-gray-800\/40:hover { background-color: rgba(31, 41, 55, 0.4); }
.hover\:text-white:hover { color: #ffffff; }
.hover\:underline:hover { text-decoration: underline; }
.hover\:underline-offset-4:hover { text-underline-offset: 4px; }
.hover\:border-transparent:hover { border-color: transparent; }

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

/* Backdrop */
.backdrop-blur-sm { backdrop-filter: blur(4px); }

/* Responsive Design */
@media (min-width: 640px) {
    .sm\:p-20 { padding: 5rem; }
    .sm\:text-left { text-align: left; }
    .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .sm\:h-12 { height: 3rem; }
    .sm\:px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
    .sm\:w-auto { width: auto; }
    .sm\:flex-row { flex-direction: row; }
    .sm\:items-start { align-items: flex-start; }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:w-\[158px\] { width: 158px; }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.max-w-4xl { max-width: 56rem; }
.shrink-0 { flex-shrink: 0; }
.list-inside { list-style-position: inside; }
.list-decimal { list-style-type: decimal; }
.tracking-\[-\.01em\] { letter-spacing: -0.01em; }

/* Custom Styles for CYPHER theme */
.bg-black\/\[\.05\] { background-color: rgba(0, 0, 0, 0.05); }
.bg-white\/\[\.06\] { background-color: rgba(255, 255, 255, 0.06); }
.bg-gray-900\/30 { background-color: rgba(17, 24, 39, 0.3); }
.bg-gray-900\/20 { background-color: rgba(17, 24, 39, 0.2); }
.bg-black\/90 { background-color: rgba(0, 0, 0, 0.9); }

/* Dark theme invert for images */
.dark\:invert {
    filter: invert(1) !important;
}

/* Ensure images are visible in dark theme */
html.dark .dark\:invert,
.dark .dark\:invert,
body.dark .dark\:invert {
    filter: invert(1) !important;
}

/* Force invert for all images with dark:invert class */
img.dark\:invert {
    filter: invert(1) !important;
}

/* Light Theme Variables */
.light {
    --background: #ffffff;
    --foreground: #111827;
    --card: #f9fafb;
    --card-foreground: #111827;
    --popover: #ffffff;
    --popover-foreground: #111827;
    --primary: #111827;
    --primary-foreground: #ffffff;
    --secondary: #f3f4f6;
    --secondary-foreground: #111827;
    --muted: #f3f4f6;
    --muted-foreground: #6b7280;
    --accent: #f3f4f6;
    --accent-foreground: #111827;
    --destructive: #ef4444;
    --border: rgba(0, 0, 0, 0.1);
    --input: rgba(0, 0, 0, 0.05);
    --ring: #9ca3af;
    --sidebar: #f9fafb;
    --sidebar-foreground: #111827;
    --sidebar-border: rgba(0, 0, 0, 0.1);
}

/* Light theme styles */
.light body {
    background-color: var(--background);
    color: var(--foreground);
}

.light .bg-black { background-color: #ffffff; }
.light .bg-gray-900 { background-color: #f9fafb; }
.light .bg-gray-800 { background-color: #f3f4f6; }
.light .text-white { color: #111827; }
.light .text-gray-300 { color: #4b5563; }
.light .text-gray-400 { color: #6b7280; }
.light .text-gray-500 { color: #9ca3af; }
.light .border-gray-700 { border-color: #d1d5db; }
.light .border-gray-800 { border-color: #e5e7eb; }

/* Theme transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
