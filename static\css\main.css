/* CSS Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables - Professional Dark Theme */
:root {
    --radius: 0.5rem;

    /* Core Colors - Monochrome */
    --background: #000000;
    --foreground: #ffffff;
    --card: #111111;
    --card-foreground: #ffffff;
    --popover: #1a1a1a;
    --popover-foreground: #ffffff;

    /* Primary Colors - White/Light Gray */
    --primary: #ffffff;
    --primary-foreground: #000000;
    --primary-hover: #f5f5f5;

    /* Secondary Colors - Dark Gray */
    --secondary: #1a1a1a;
    --secondary-foreground: #e5e5e5;
    --secondary-hover: #2a2a2a;

    /* Muted Colors - Medium Gray */
    --muted: #2a2a2a;
    --muted-foreground: #a1a1a1;

    /* Accent Colors - Dark Blue (minimal use) */
    --accent: #1e3a8a;
    --accent-foreground: #ffffff;
    --accent-hover: #1e40af;
    --accent-light: #3b82f6;

    /* Status Colors - Grayscale */
    --success: #ffffff;
    --warning: #d1d5db;
    --destructive: #6b7280;

    /* Borders - Gray tones */
    --border: #333333;
    --border-light: #404040;
    --border-accent: #1e3a8a;
    --input: #1a1a1a;
    --ring: var(--accent);

    /* Sidebar - Darker theme */
    --sidebar: #0a0a0a;
    --sidebar-foreground: #ffffff;
    --sidebar-border: #1a1a1a;

    /* Shadows - Subtle */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.8);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.6);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);

    /* Gradients - Monochrome */
    --gradient-primary: linear-gradient(135deg, #ffffff, #e5e5e5);
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #000000);
    --gradient-card: linear-gradient(135deg, #111111, #0a0a0a);
    --gradient-accent: linear-gradient(135deg, #1e3a8a, #1e40af);

    /* Fonts */
    --font-geist-sans: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-geist-mono: 'Geist Mono', 'Fira Code', 'Consolas', monospace;
}

/* Base Styles */
html {
    font-family: var(--font-geist-sans);
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    background: var(--background);
    color: var(--foreground);
    font-family: var(--font-geist-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background);
}

::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
    box-shadow: var(--glow-primary);
}

/* Typography */
.font-mono {
    font-family: var(--font-geist-mono);
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

/* Text Sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

/* Colors */
.text-white { color: var(--foreground); }
.text-black { color: var(--background); }
.text-primary { color: var(--primary); }
.text-accent { color: var(--accent); }
.text-muted { color: var(--muted-foreground); }
.text-secondary { color: var(--secondary-foreground); }

.bg-primary { background-color: var(--primary); }
.bg-secondary { background-color: var(--secondary); }
.bg-card { background-color: var(--card); }
.bg-muted { background-color: var(--muted); }

/* Professional Effects */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-card {
    background: var(--gradient-card);
}

.gradient-accent {
    background: var(--gradient-accent);
}

/* Layout */
.min-h-screen { min-height: 100vh; }
.h-screen { height: 100vh; }
.w-full { width: 100%; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Flexbox */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-items-center { justify-items: center; }
.flex-1 { flex: 1 1 0%; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.row-start-2 { grid-row-start: 2; }
.row-start-3 { grid-row-start: 3; }

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.pl-4 { padding-left: 1rem; }
.pl-6 { padding-left: 1.5rem; }
.pb-20 { padding-bottom: 5rem; }

.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-auto { margin-top: auto; }
.ml-4 { margin-left: 1rem; }
.ml-6 { margin-left: 1.5rem; }
.mr-1 { margin-right: 0.25rem; }

/* Gaps */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-16 { gap: 4rem; }

/* Borders */
.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-r { border-right-width: 1px; }
.border-l { border-left-width: 1px; }
.border-t { border-top-width: 1px; }
.border-gray-700 { border-color: #374151; }
.border-gray-800 { border-color: #1f2937; }
.border-solid { border-style: solid; }
.border-transparent { border-color: transparent; }

/* Border Radius */
.rounded { border-radius: 0.25rem; }
.rounded-full { border-radius: 9999px; }

/* Sizing */
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-6 { width: 1.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-80 { width: 20rem; }
.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-6 { height: 1.5rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-auto { height: auto; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.top-2 { top: 0.5rem; }
.right-2 { right: 0.5rem; }
.z-10 { z-index: 10; }

/* Overflow */
.overflow-y-auto { overflow-y: auto; }

/* Opacity */
.opacity-60 { opacity: 0.6; }

/* Transitions */
.transition-all { transition: all 0.15s ease-in-out; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }

/* Hover States */
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:bg-gray-800\/40:hover { background-color: rgba(31, 41, 55, 0.4); }
.hover\:text-white:hover { color: #ffffff; }
.hover\:underline:hover { text-decoration: underline; }
.hover\:underline-offset-4:hover { text-underline-offset: 4px; }
.hover\:border-transparent:hover { border-color: transparent; }

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

/* Backdrop */
.backdrop-blur-sm { backdrop-filter: blur(4px); }

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column;
    }

    .mobile-full {
        width: 100%;
    }

    .mobile-text-center {
        text-align: center;
    }

    .mobile-p-4 {
        padding: 1rem;
    }
}

@media (min-width: 640px) {
    .sm\:p-20 { padding: 5rem; }
    .sm\:text-left { text-align: left; }
    .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .sm\:h-12 { height: 3rem; }
    .sm\:px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
    .sm\:w-auto { width: auto; }
    .sm\:flex-row { flex-direction: row; }
    .sm\:items-start { align-items: flex-start; }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    .md\:w-\[158px\] { width: 158px; }
}

/* Dark Mode Optimizations */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: dark;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.max-w-4xl { max-width: 56rem; }
.shrink-0 { flex-shrink: 0; }
.list-inside { list-style-position: inside; }
.list-decimal { list-style-type: decimal; }
.tracking-\[-\.01em\] { letter-spacing: -0.01em; }

/* Enhanced Button Styles */
button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    outline: none;
}

button:hover {
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

/* Primary Button */
.btn-primary {
    background: var(--primary);
    color: var(--primary-foreground);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid var(--border);
}

.btn-primary:hover {
    background: var(--primary-hover);
    border-color: var(--accent);
}

/* Secondary Button */
.btn-secondary {
    background: var(--secondary);
    color: var(--secondary-foreground);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    border: 1px solid var(--border);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: var(--secondary-hover);
    border-color: var(--accent);
}

/* Card Enhancements */
.card-enhanced {
    background: var(--gradient-card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    transition: all 0.3s ease;
}

.card-enhanced:hover {
    border-color: var(--border-accent);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Input Enhancements */
input, textarea, select {
    background: var(--input);
    border: 1px solid var(--border);
    color: var(--foreground);
    border-radius: var(--radius);
    padding: 0.75rem;
    transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary-glow);
    outline: none;
}

/* Sidebar Navigation Styles */
.sidebar-phase-button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-phase-button:hover {
    transform: translateX(2px);
}

.sidebar-module-button {
    transition: all 0.2s ease;
}

.sidebar-module-button:hover {
    transform: translateX(1px);
}

.sidebar-topic-button {
    transition: all 0.15s ease;
}

.sidebar-topic-button:hover {
    transform: translateX(1px);
}

/* Phase Icon Container */
.phase-icon-container {
    background: var(--border-light);
    transition: all 0.3s ease;
}

.sidebar-phase-button:hover .phase-icon-container {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Module Icon Container */
.module-icon-container {
    background: var(--border-light);
    transition: all 0.3s ease;
}

.sidebar-module-button:hover .module-icon-container {
    background: rgba(255, 255, 255, 0.2);
}

/* Topic Indicator */
.topic-indicator {
    background: var(--primary);
    transition: all 0.3s ease;
}

.sidebar-topic-button:hover .topic-indicator {
    background: var(--primary);
    transform: scale(1.2);
}

/* Chevron Animation */
.chevron-icon {
    transition: transform 0.2s ease;
}

.chevron-icon.expanded {
    transform: rotate(90deg);
}

/* Sidebar Content Animation */
.sidebar-content {
    transition: all 0.3s ease;
    overflow: hidden;
}

/* Phase Title Styling */
.phase-title {
    font-weight: 700;
    letter-spacing: 0.025em;
}

/* Module Title Styling */
.module-title {
    font-weight: 500;
    letter-spacing: 0.01em;
}

/* Topic Styling */
.topic-item {
    font-weight: 400;
    opacity: 0.9;
}

.topic-item:hover {
    opacity: 1;
}

/* Sidebar Scrollbar */
#study-sidebar::-webkit-scrollbar {
    width: 6px;
}

#study-sidebar::-webkit-scrollbar-track {
    background: var(--sidebar);
}

#study-sidebar::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

#study-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--accent);
}

/* Markdown Content Styles */
.prose {
    color: var(--foreground);
    max-width: none;
}

.prose h1 {
    color: var(--primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--border);
    padding-bottom: 0.5rem;
}

.prose h2 {
    color: var(--primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose h3 {
    color: var(--accent);
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.prose p {
    color: var(--secondary-foreground);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.prose ul, .prose ol {
    color: var(--secondary-foreground);
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.prose li {
    margin-bottom: 0.5rem;
}

.prose strong {
    color: var(--primary);
    font-weight: 600;
}

.prose code {
    background: var(--muted);
    color: var(--accent);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: var(--font-geist-mono);
    font-size: 0.875rem;
}

.prose pre {
    background: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem;
    overflow-x: auto;
    margin-bottom: 1rem;
}

.prose pre code {
    background: transparent;
    padding: 0;
    color: var(--foreground);
}

.prose blockquote {
    border-left: 4px solid var(--accent);
    background: var(--muted);
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 var(--radius) var(--radius) 0;
    font-style: italic;
    color: var(--secondary-foreground);
}

.prose table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    border: 1px solid var(--border);
}

.prose th, .prose td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.prose th {
    background: var(--muted);
    font-weight: 600;
    color: var(--primary);
}

/* Enhanced Matrix Rain Effect */
.matrix-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 20px 20px, 20px 20px, 40px 40px, 60px 60px;
    animation: matrix-scroll 25s linear infinite, matrix-pulse 8s ease-in-out infinite;
}

@keyframes matrix-scroll {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

@keyframes matrix-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
}

/* Enhanced Terminal Cards */
.terminal-card {
    background: rgba(17, 17, 17, 0.3);
    border: 1px solid rgba(107, 114, 128, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.terminal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.terminal-card:hover {
    background: rgba(17, 17, 17, 0.5);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

.terminal-card:hover::before {
    left: 100%;
}

/* Enhanced Terminal Button */
.terminal-button {
    background: transparent;
    border: 1px solid rgba(107, 114, 128, 0.6);
    color: var(--primary);
    font-family: var(--font-geist-mono);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.terminal-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.terminal-button:hover {
    background: rgba(17, 17, 17, 0.5);
    border-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.15);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.terminal-button:hover::before {
    left: 100%;
}

/* Enhanced Title Effects */
.cypher-title {
    background: linear-gradient(45deg, #ffffff, #f0f0f0, #ffffff);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: titleShimmer 4s ease-in-out infinite, titleFloat 6s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

@keyframes titleShimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes titleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Prompt Symbol Animation */
.prompt-symbol {
    animation: promptPulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
}

@keyframes promptPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
}

/* Enhanced Typing Cursor */
.typing-cursor {
    animation: cursorBlink 1.2s infinite, cursorGlow 2s ease-in-out infinite;
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6));
}

@keyframes cursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.2; }
}

@keyframes cursorGlow {
    0%, 100% { filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6)); }
    50% { filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.8)); }
}

/* Title Container Hover Effect */
.title-container:hover .cypher-title {
    animation-duration: 2s;
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.title-container:hover .prompt-symbol,
.title-container:hover .typing-cursor {
    animation-duration: 1s;
}

/* Page Entry Animations */
.page-content {
    animation: pageEntry 1.2s ease-out;
}

@keyframes pageEntry {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Staggered Card Animations */
.cards-container .card-1 {
    animation: cardEntry 0.8s ease-out 0.3s both;
}

.cards-container .card-2 {
    animation: cardEntry 0.8s ease-out 0.5s both;
}

.cards-container .card-3 {
    animation: cardEntry 0.8s ease-out 0.7s both;
}

@keyframes cardEntry {
    0% { opacity: 0; transform: translateY(40px) scale(0.9); }
    100% { opacity: 1; transform: translateY(0) scale(1); }
}

/* Subtitle Animation */
.subtitle-container {
    animation: subtitleEntry 1s ease-out 0.4s both;
}

@keyframes subtitleEntry {
    0% { opacity: 0; transform: translateX(-30px); }
    100% { opacity: 1; transform: translateX(0); }
}

/* Description Animation */
.description-container {
    animation: descriptionEntry 1s ease-out 0.6s both;
}

@keyframes descriptionEntry {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* CTA Button Animation */
.cta-container {
    animation: ctaEntry 1s ease-out 0.9s both;
}

@keyframes ctaEntry {
    0% { opacity: 0; transform: translateY(30px) scale(0.9); }
    100% { opacity: 1; transform: translateY(0) scale(1); }
}

/* Enhanced Card Icons */
.card-icon {
    animation: iconFloat 3s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(2deg); }
}

/* Classified Tag Effect */
.classified-tag {
    animation: classifiedGlow 2s ease-in-out infinite;
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
}

@keyframes classifiedGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Enhanced CTA Button */
.cta-button {
    animation: ctaPulse 3s ease-in-out infinite;
}

@keyframes ctaPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

/* Smooth scroll for anchor links */
html {
    scroll-behavior: smooth;
}

/* Custom Styles for CYPHER theme */
.bg-black\/\[\.05\] { background-color: rgba(0, 0, 0, 0.05); }
.bg-white\/\[\.06\] { background-color: rgba(255, 255, 255, 0.06); }
.bg-gray-900\/30 { background-color: rgba(17, 24, 39, 0.3); }
.bg-gray-900\/20 { background-color: rgba(17, 24, 39, 0.2); }
.bg-black\/90 { background-color: rgba(0, 0, 0, 0.9); }

/* Dark theme invert for images */
.dark\:invert {
    filter: invert(1) !important;
}

/* Ensure images are visible in dark theme */
html.dark .dark\:invert,
.dark .dark\:invert,
body.dark .dark\:invert {
    filter: invert(1) !important;
}

/* Force invert for all images with dark:invert class */
img.dark\:invert {
    filter: invert(1) !important;
}

/* Cyberpunk Animations */
@keyframes matrix-rain {
    0% { transform: translateY(-100vh); }
    100% { transform: translateY(100vh); }
}

@keyframes glow-pulse {
    0%, 100% { box-shadow: var(--glow-primary); }
    50% { box-shadow: 0 0 30px var(--primary-glow); }
}

@keyframes text-glow {
    0%, 100% { text-shadow: 0 0 5px var(--primary); }
    50% { text-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary); }
}

/* Matrix Background Effect */
.matrix-bg {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.matrix-bg::before {
    content: '';
    position: absolute;
    top: -100vh;
    left: 0;
    width: 100%;
    height: 200vh;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        var(--primary) 100px
    );
    opacity: 0.1;
    animation: matrix-rain 20s linear infinite;
}

/* Enhanced Gradient Text */
.gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Hover Effects */
.hover-glow:hover {
    animation: glow-pulse 2s ease-in-out infinite;
}

.hover-text-glow:hover {
    animation: text-glow 2s ease-in-out infinite;
}

/* Enhanced Card Styles */
.cyber-card {
    background: var(--gradient-card);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 255, 136, 0.1),
        transparent
    );
    transition: left 0.5s ease;
}

.cyber-card:hover::before {
    left: 100%;
}

/* Terminal Cursor */
@keyframes cursor-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.terminal-cursor::after {
    content: '█';
    animation: cursor-blink 1s infinite;
    color: var(--primary);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        var(--primary-glow),
        transparent
    );
    animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Focus States */
*:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--radius);
}

/* Selection */
::selection {
    background: var(--primary-glow);
    color: var(--foreground);
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* Links */
a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-hover);
    text-shadow: 0 0 5px var(--primary-glow);
}

/* Code Blocks */
code, pre {
    font-family: var(--font-geist-mono);
    background: var(--muted);
    border-radius: var(--radius);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

pre {
    padding: 1rem;
    overflow-x: auto;
    border: 1px solid var(--border);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

th {
    background: var(--muted);
    font-weight: 600;
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

label {
    font-weight: 500;
    margin-bottom: 0.25rem;
    display: block;
}

/* Light Theme Variables */
.light {
    --background: #ffffff;
    --foreground: #111827;
    --card: #f9fafb;
    --card-foreground: #111827;
    --popover: #ffffff;
    --popover-foreground: #111827;
    --primary: #111827;
    --primary-foreground: #ffffff;
    --secondary: #f3f4f6;
    --secondary-foreground: #111827;
    --muted: #f3f4f6;
    --muted-foreground: #6b7280;
    --accent: #f3f4f6;
    --accent-foreground: #111827;
    --destructive: #ef4444;
    --border: rgba(0, 0, 0, 0.1);
    --input: rgba(0, 0, 0, 0.05);
    --ring: #9ca3af;
    --sidebar: #f9fafb;
    --sidebar-foreground: #111827;
    --sidebar-border: rgba(0, 0, 0, 0.1);
}

/* Light theme styles */
.light body {
    background-color: var(--background);
    color: var(--foreground);
}

.light .bg-black { background-color: #ffffff; }
.light .bg-gray-900 { background-color: #f9fafb; }
.light .bg-gray-800 { background-color: #f3f4f6; }
.light .text-white { color: #111827; }
.light .text-gray-300 { color: #4b5563; }
.light .text-gray-400 { color: #6b7280; }
.light .text-gray-500 { color: #9ca3af; }
.light .border-gray-700 { border-color: #d1d5db; }
.light .border-gray-800 { border-color: #e5e7eb; }

/* Theme transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
