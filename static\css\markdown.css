/* Markdown Content Styling for CYPHER */

.markdown-content {
    line-height: 1.7;
    color: #e5e7eb;
}

/* Headings */
.markdown-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #374151;
}

.markdown-content h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #ffffff;
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.3rem;
    border-bottom: 1px solid #374151;
}

.markdown-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 1.25rem 0 0.75rem 0;
}

.markdown-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 1rem 0 0.5rem 0;
}

.markdown-content h5,
.markdown-content h6 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 0.75rem 0 0.5rem 0;
}

/* Paragraphs */
.markdown-content p {
    margin: 1rem 0;
    color: #d1d5db;
}

/* Links */
.markdown-content a {
    color: #60a5fa;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.markdown-content a:hover {
    color: #93c5fd;
    border-bottom-color: #60a5fa;
}

/* Lists */
.markdown-content ul,
.markdown-content ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

.markdown-content li {
    margin: 0.5rem 0;
    color: #d1d5db;
}

.markdown-content ul li {
    list-style-type: disc;
}

.markdown-content ol li {
    list-style-type: decimal;
}

/* Nested lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
    margin: 0.25rem 0;
}

/* Code */
.markdown-content code {
    background-color: #1f2937;
    color: #f59e0b;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-family: 'Geist Mono', 'Fira Code', 'Consolas', monospace;
    font-size: 0.875rem;
    border: 1px solid #374151;
}

.markdown-content pre {
    background-color: #111827;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    position: relative;
}

.markdown-content pre code {
    background: none;
    border: none;
    padding: 0;
    color: #e5e7eb;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Code block language label */
.markdown-content pre::before {
    content: attr(data-lang);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: #374151;
    color: #9ca3af;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-family: 'Geist Mono', monospace;
}

/* Syntax highlighting */
.markdown-content .codehilite {
    background-color: #111827;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    overflow: hidden;
}

.markdown-content .codehilite pre {
    margin: 0;
    border: none;
    border-radius: 0;
}

/* Blockquotes */
.markdown-content blockquote {
    border-left: 4px solid #60a5fa;
    background-color: #1f2937;
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-radius: 0 0.5rem 0.5rem 0;
}

.markdown-content blockquote p {
    margin: 0.5rem 0;
    color: #d1d5db;
    font-style: italic;
}

/* Tables */
.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background-color: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    overflow: hidden;
}

.markdown-content th,
.markdown-content td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #374151;
}

.markdown-content th {
    background-color: #111827;
    font-weight: 600;
    color: #ffffff;
}

.markdown-content td {
    color: #d1d5db;
}

.markdown-content tr:last-child td {
    border-bottom: none;
}

/* Horizontal Rule */
.markdown-content hr {
    border: none;
    height: 1px;
    background-color: #374151;
    margin: 2rem 0;
}

/* Images */
.markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
    border: 1px solid #374151;
}

/* Emphasis */
.markdown-content strong {
    font-weight: 700;
    color: #ffffff;
}

.markdown-content em {
    font-style: italic;
    color: #f3f4f6;
}

/* Task Lists */
.markdown-content input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: #60a5fa;
}

/* Keyboard Keys */
.markdown-content kbd {
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-family: 'Geist Mono', monospace;
    font-size: 0.75rem;
    color: #f3f4f6;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Admonitions/Callouts */
.markdown-content .admonition {
    margin: 1.5rem 0;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.markdown-content .admonition.note {
    background-color: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
}

.markdown-content .admonition.warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
}

.markdown-content .admonition.danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
}

.markdown-content .admonition.success {
    background-color: rgba(34, 197, 94, 0.1);
    border-left-color: #22c55e;
}

/* Emoji and Icons */
.markdown-content .emoji {
    font-size: 1.2em;
    vertical-align: middle;
}

/* Print Styles */
@media print {
    .markdown-content {
        color: #000;
        background: #fff;
    }
    
    .markdown-content pre,
    .markdown-content code {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
    }
    
    .markdown-content blockquote {
        background-color: #f9f9f9;
        border-left-color: #333;
    }
    
    .markdown-content table {
        background-color: #fff;
        border: 1px solid #ddd;
    }
    
    .markdown-content th {
        background-color: #f5f5f5;
    }
}

/* Light Theme Support */
.light .markdown-content {
    color: #374151;
}

.light .markdown-content h1,
.light .markdown-content h2,
.light .markdown-content h3,
.light .markdown-content h4,
.light .markdown-content h5,
.light .markdown-content h6 {
    color: #111827;
}

.light .markdown-content code {
    background-color: #f3f4f6;
    color: #dc2626;
    border-color: #d1d5db;
}

.light .markdown-content pre {
    background-color: #f9fafb;
    border-color: #d1d5db;
}

.light .markdown-content blockquote {
    background-color: #f3f4f6;
    border-left-color: #3b82f6;
}

.light .markdown-content table {
    background-color: #ffffff;
    border-color: #d1d5db;
}

.light .markdown-content th {
    background-color: #f9fafb;
    color: #111827;
}

.light .markdown-content td {
    color: #374151;
    border-color: #d1d5db;
}
