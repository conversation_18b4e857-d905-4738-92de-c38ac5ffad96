# 1.1 Introdução à Segurança da Informação

> **📍 Localização no Currículo CYPHER**
> **FASE 1: FUNDAMENTOS** → **Módulo 1.1: Introdução à Segurança**
> Duração: 8-12 semanas | Pré-requisitos: Nenhum

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Compreender os conceitos fundamentais de segurança da informação
- Identificar os pilares da segurança (Tríade CIA)
- Reconhecer os principais tipos de ameaças
- Entender a importância da segurança em diferentes contextos
- Preparar-se para os próximos módulos da **FASE 1: FUNDAMENTOS**

## O que é Segurança da Informação?

A **Segurança da Informação** é a prática de proteger informações digitais e físicas contra acesso não autorizado, uso indevido, divulgação, interrupção, modificação ou destruição.

### Por que é Importante?

- 🔒 **Proteção de dados pessoais e corporativos**
- 💰 **Prevenção de perdas financeiras**
- 🏛️ **Conformidade com regulamentações**
- 🛡️ **Manutenção da reputação organizacional**

## A Tríade CIA

A base da segurança da informação é fundamentada em três pilares principais:

### 🔐 Confidencialidade (Confidentiality)
- Garantir que apenas pessoas autorizadas tenham acesso às informações
- **Exemplo**: Criptografia de dados sensíveis

### 🔧 Integridade (Integrity)
- Assegurar que os dados não sejam alterados de forma não autorizada
- **Exemplo**: Checksums e assinaturas digitais

### ⚡ Disponibilidade (Availability)
- Garantir que os sistemas e dados estejam acessíveis quando necessário
- **Exemplo**: Sistemas de backup e redundância

## Tipos de Ameaças

### Ameaças Internas
- Funcionários mal-intencionados
- Erros humanos não intencionais
- Negligência de segurança

### Ameaças Externas
- **Hackers**: Indivíduos com conhecimento técnico
- **Crackers**: Hackers com intenções maliciosas
- **Script Kiddies**: Usuários com pouco conhecimento técnico
- **Grupos organizados**: Crime organizado e espionagem

### Ameaças Físicas
- Desastres naturais
- Falhas de hardware
- Roubo de equipamentos

## Conceitos Fundamentais

### Vulnerabilidade
Uma **vulnerabilidade** é uma fraqueza em um sistema que pode ser explorada por uma ameaça.

```bash
# Exemplo: Verificação de vulnerabilidades
nmap -sV --script vuln target.com
```

### Exploit
Um **exploit** é um código ou técnica que aproveita uma vulnerabilidade específica.

### Risco
**Risco** = Probabilidade × Impacto

O risco é calculado considerando a probabilidade de uma ameaça explorar uma vulnerabilidade e o impacto resultante.

## Frameworks de Segurança

### NIST Cybersecurity Framework
1. **Identify** (Identificar)
2. **Protect** (Proteger)
3. **Detect** (Detectar)
4. **Respond** (Responder)
5. **Recover** (Recuperar)

### ISO 27001
Padrão internacional para sistemas de gestão de segurança da informação (SGSI).

## Exercícios Práticos

### Exercício 1: Identificação de Ameaças
Liste 5 possíveis ameaças para uma empresa de e-commerce:

1. ___________________
2. ___________________
3. ___________________
4. ___________________
5. ___________________

### Exercício 2: Análise da Tríade CIA
Para cada cenário, identifique qual pilar da CIA foi comprometido:

- [ ] Um funcionário acessa dados de clientes sem autorização
- [ ] Um vírus corrompe o banco de dados da empresa
- [ ] O servidor web fica fora do ar por 6 horas

## Recursos Adicionais

- 📚 [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- 🎓 [SANS Security Awareness](https://www.sans.org/security-awareness-training/)
- 🔗 [OWASP Foundation](https://owasp.org/)

## Conexões com o Currículo CYPHER

### 🔗 Módulos Relacionados na FASE 1

Este capítulo é a base para todos os outros módulos da **FASE 1: FUNDAMENTOS**:

- **[1.2 Fundamentos de SO](/capitulos/02-fundamentos-so)** - Aplicação dos conceitos de segurança em sistemas operacionais
- **[1.3 Fundamentos de Redes](/capitulos/03-fundamentos-redes)** - Segurança em infraestrutura de rede
- **[1.4 Programação para Segurança](/capitulos/04-programacao-seguranca)** - Desenvolvimento seguro e automação

### 🚀 Preparação para Fases Avançadas

Os conceitos aprendidos aqui são fundamentais para:

- **FASE 2: RECONHECIMENTO** - OSINT e análise de vulnerabilidades
- **FASE 3: WEB SECURITY** - Aplicação da tríade CIA em aplicações web
- **FASE 4: INFRAESTRUTURA** - Hardening e segurança de sistemas
- **FASE 8: ESPECIALIZAÇÃO** - Análise de malware e forense digital

### 📋 Checklist de Progresso

Antes de avançar para o próximo módulo, certifique-se de que você:

- [ ] Compreende completamente a Tríade CIA
- [ ] Consegue identificar diferentes tipos de ameaças
- [ ] Entende os frameworks NIST e ISO 27001
- [ ] Completou todos os exercícios práticos
- [ ] Pode explicar os conceitos para outra pessoa

## Próximos Passos

### 📚 Próximo Módulo: [1.2 Fundamentos de SO](/capitulos/02-fundamentos-so)

No próximo capítulo da **FASE 1**, exploraremos como os **Fundamentos de Sistemas Operacionais** se relacionam com a segurança da informação, aplicando os conceitos da Tríade CIA em ambientes Windows e Linux.

### 🎯 Objetivos do Próximo Módulo:
- Arquitetura de segurança em Windows e Linux
- Comandos essenciais para análise de segurança
- Hardening básico de sistemas operacionais
- Logs e monitoramento de segurança

---

**📊 Progresso na FASE 1**: 1/4 módulos concluídos
**⏱️ Tempo estimado de estudo**: 2-3 horas
**🎯 Nível de dificuldade**: ⭐⭐☆☆☆
**🔗 Módulo ID**: `intro-security` (CYPHER Database)
