from flask import Flask, render_template, jsonify, request, abort
import os
import markdown
from datetime import datetime
import re
from pathlib import Path

app = Flask(__name__)

# Configuração para servir arquivos estáticos
app.static_folder = 'static'
app.template_folder = 'templates'

# Configuração do Markdown
md = markdown.Markdown(extensions=[
    'codehilite',
    'fenced_code',
    'tables',
    'toc'
])

# Diretório dos capítulos
CONTENT_DIR = Path('content/capitulos')

def get_chapter_list():
    """Retorna lista de capítulos disponíveis"""
    chapters = []
    if CONTENT_DIR.exists():
        for file_path in sorted(CONTENT_DIR.glob('*.md')):
            # Extrair informações do arquivo
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extrair título (primeira linha com #)
            title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
            title = title_match.group(1) if title_match else file_path.stem

            chapters.append({
                'id': file_path.stem,
                'title': title,
                'filename': file_path.name,
                'path': str(file_path)
            })

    return chapters

def get_chapter_content(chapter_id):
    """Retorna conteúdo de um capítulo específico"""
    chapter_file = CONTENT_DIR / f"{chapter_id}.md"

    if not chapter_file.exists():
        return None

    with open(chapter_file, 'r', encoding='utf-8') as f:
        markdown_content = f.read()

    # Converter markdown para HTML
    html_content = md.convert(markdown_content)

    # Extrair título
    title_match = re.search(r'^#\s+(.+)$', markdown_content, re.MULTILINE)
    title = title_match.group(1) if title_match else chapter_id

    return {
        'id': chapter_id,
        'title': title,
        'content': html_content,
        'markdown': markdown_content
    }

def search_chapters(query):
    """Busca por termo nos capítulos"""
    results = []
    chapters = get_chapter_list()

    for chapter in chapters:
        chapter_content = get_chapter_content(chapter['id'])
        if chapter_content and query.lower() in chapter_content['markdown'].lower():
            # Encontrar contexto da busca
            lines = chapter_content['markdown'].split('\n')
            matches = []

            for i, line in enumerate(lines):
                if query.lower() in line.lower():
                    # Contexto: linha anterior, atual e próxima
                    context_start = max(0, i - 1)
                    context_end = min(len(lines), i + 2)
                    context = '\n'.join(lines[context_start:context_end])

                    matches.append({
                        'line_number': i + 1,
                        'context': context,
                        'line': line
                    })

            if matches:
                results.append({
                    'chapter': chapter,
                    'matches': matches
                })

    return results

@app.route('/')
def home():
    """Página inicial do CYPHER"""
    return render_template('index.html')

@app.route('/estudos')
def estudos():
    """Página principal de estudos com dashboard"""
    return render_template('estudos.html')

@app.route('/capitulos')
def capitulos():
    """Lista de capítulos disponíveis"""
    chapters = get_chapter_list()
    return render_template('capitulos.html', chapters=chapters)

@app.route('/capitulos/<chapter_id>')
def capitulo(chapter_id):
    """Visualizar capítulo específico"""
    chapter = get_chapter_content(chapter_id)
    if not chapter:
        abort(404)

    chapters = get_chapter_list()

    # Encontrar capítulo anterior e próximo
    current_index = next((i for i, c in enumerate(chapters) if c['id'] == chapter_id), -1)
    prev_chapter = chapters[current_index - 1] if current_index > 0 else None
    next_chapter = chapters[current_index + 1] if current_index < len(chapters) - 1 else None

    return render_template('capitulo.html',
                         chapter=chapter,
                         chapters=chapters,
                         prev_chapter=prev_chapter,
                         next_chapter=next_chapter)

@app.route('/api/time')
def get_current_time():
    """API para obter horário atual"""
    return jsonify({
        'time': datetime.now().strftime('%H:%M:%S'),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/connection-status')
def get_connection_status():
    """API para simular status de conexão"""
    import random
    return jsonify({
        'connected': random.choice([True, False]),
        'status': 'CONNECTED' if random.choice([True, False]) else 'RECONNECTING...'
    })

# Dados do currículo de estudos
STUDY_CONTENT = [
    {
        "id": "fase1",
        "title": "[FASE 1: FUNDAMENTOS]",
        "icon": "shield",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "intro-security",
                "title": "1.1 Introdução à Segurança",
                "icon": "lock",
                "topics": ["Conceitos Fundamentais", "Tríade CIA", "Tipos de Ataques", "Frameworks de Segurança"]
            },
            {
                "id": "os-fundamentals",
                "title": "1.2 Fundamentos de SO",
                "icon": "terminal",
                "topics": ["Windows Fundamentals", "Linux Fundamentals", "Windows Avançado", "Linux Avançado"]
            },
            {
                "id": "network-fundamentals",
                "title": "1.3 Fundamentos de Redes",
                "icon": "network",
                "topics": ["Conceitos de Rede", "Protocolos Essenciais", "Análise de Tráfego", "Segurança de Rede"]
            },
            {
                "id": "programming",
                "title": "1.4 Programação para Segurança",
                "icon": "code",
                "topics": ["Python Fundamentals", "Python para Segurança", "Bash Scripting", "Git e Versionamento"]
            }
        ]
    },
    {
        "id": "fase2",
        "title": "[FASE 2: RECONHECIMENTO]",
        "icon": "search",
        "duration": "10-14 semanas",
        "items": [
            {
                "id": "osint",
                "title": "2.1 OSINT",
                "icon": "eye",
                "topics": ["Introdução ao OSINT", "Ferramentas Básicas", "OSINT Avançado", "OSINT Profissional"]
            },
            {
                "id": "vulnerability-analysis",
                "title": "2.2 Análise de Vulnerabilidades",
                "icon": "bug",
                "topics": ["Conceitos de Vulnerabilidade", "Ferramentas de Scanning", "Scanning Avançado", "Vulnerability Management"]
            },
            {
                "id": "social-engineering",
                "title": "2.3 Engenharia Social",
                "icon": "users",
                "topics": ["Psicologia da Segurança", "Tipos de Ataques", "Simulações Éticas", "Awareness e Treinamento"]
            }
        ]
    },
    {
        "id": "fase3",
        "title": "[FASE 3: WEB SECURITY]",
        "icon": "globe",
        "duration": "14-18 semanas",
        "items": [
            {
                "id": "web-dev",
                "title": "3.1 Desenvolvimento Web",
                "icon": "code",
                "topics": ["Tecnologias Web", "Arquitetura Web", "Desenvolvimento Backend"]
            },
            {
                "id": "owasp-top10",
                "title": "3.2 OWASP Top 10",
                "icon": "shield",
                "topics": ["OWASP Top 10 - Parte 1", "Ferramentas Básicas", "OWASP Top 10 - Parte 2", "Vulnerabilidades Avançadas"]
            },
            {
                "id": "apis-modern",
                "title": "3.3 APIs e Apps Modernas",
                "icon": "network",
                "topics": ["API Security", "Tecnologias Modernas"]
            }
        ]
    },
    {
        "id": "fase4",
        "title": "[FASE 4: INFRAESTRUTURA]",
        "icon": "server",
        "duration": "16-20 semanas",
        "items": [
            {
                "id": "network-security",
                "title": "4.1 Segurança de Redes",
                "icon": "network",
                "topics": ["Hardening de Rede", "Protocolos Seguros", "Ataques de Rede", "Técnicas Avançadas"]
            },
            {
                "id": "windows-security",
                "title": "4.2 Segurança Windows",
                "icon": "terminal",
                "topics": ["Hardening Windows", "Active Directory Basics", "Windows Security", "Advanced Windows Attacks"]
            },
            {
                "id": "linux-security",
                "title": "4.3 Segurança Linux",
                "icon": "terminal",
                "topics": ["Linux Hardening", "Monitoring e Logs", "Linux Security", "Advanced Linux Security"]
            }
        ]
    },
    {
        "id": "fase5",
        "title": "[FASE 5: CLOUD SECURITY]",
        "icon": "cloud",
        "duration": "12-16 semanas",
        "items": [
            {
                "id": "cloud-fundamentals",
                "title": "5.1 Cloud Computing",
                "icon": "cloud",
                "topics": ["Cloud Basics", "Cloud Security Fundamentals", "AWS Security", "Azure Security"]
            },
            {
                "id": "container-k8s",
                "title": "5.2 Container & Kubernetes",
                "icon": "server",
                "topics": ["Container Basics", "Container Security", "Kubernetes Security"]
            }
        ]
    },
    {
        "id": "fase6",
        "title": "[FASE 6: MOBILE SECURITY]",
        "icon": "smartphone",
        "duration": "10-14 semanas",
        "items": [
            {
                "id": "android-security",
                "title": "6.1 Android Security",
                "icon": "smartphone",
                "topics": ["Android Fundamentals", "Static Analysis", "Dynamic Analysis"]
            },
            {
                "id": "ios-security",
                "title": "6.2 iOS Security",
                "icon": "smartphone",
                "topics": ["iOS Fundamentals"]
            }
        ]
    },
    {
        "id": "fase7",
        "title": "[FASE 7: FERRAMENTAS]",
        "icon": "wrench",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "pentest-tools",
                "title": "7.1 Ferramentas de Pentest",
                "icon": "target",
                "topics": ["Kali Linux", "Metasploit Framework", "Automação"]
            },
            {
                "id": "siem-detection",
                "title": "7.2 SIEM e Detecção",
                "icon": "eye",
                "topics": ["SIEM Fundamentals", "Threat Hunting"]
            }
        ]
    },
    {
        "id": "fase8",
        "title": "[FASE 8: ESPECIALIZAÇÃO]",
        "icon": "microscope",
        "duration": "16-24 semanas",
        "items": [
            {
                "id": "malware-analysis",
                "title": "8.1 Malware Analysis",
                "icon": "bug",
                "topics": ["Static Analysis", "Dynamic Analysis", "Reverse Engineering"]
            },
            {
                "id": "digital-forensics",
                "title": "8.2 Digital Forensics",
                "icon": "search",
                "topics": ["Forensics Fundamentals", "Network Forensics"]
            },
            {
                "id": "exploit-dev",
                "title": "8.3 Exploit Development",
                "icon": "code",
                "topics": ["Assembly Language", "Buffer Overflows"]
            }
        ]
    },
    {
        "id": "fase9",
        "title": "[FASE 9: CERTIFICAÇÕES]",
        "icon": "award",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "certifications",
                "title": "9.1 Certificações Essenciais",
                "icon": "award",
                "topics": ["CompTIA Security+", "CEH (Certified Ethical Hacker)", "CISSP", "OSCP"]
            },
            {
                "id": "career-dev",
                "title": "9.2 Desenvolvimento de Carreira",
                "icon": "target",
                "topics": ["Soft Skills", "Portfolio Development"]
            }
        ]
    }
]

@app.route('/api/study-content')
def get_study_content():
    """API para obter conteúdo de estudos"""
    return jsonify(STUDY_CONTENT)

@app.route('/api/chapters')
def api_chapters():
    """API para obter lista de capítulos"""
    return jsonify(get_chapter_list())

@app.route('/api/search')
def api_search():
    """API para busca nos capítulos"""
    query = request.args.get('q', '').strip()
    if not query or len(query) < 3:
        return jsonify({'error': 'Query deve ter pelo menos 3 caracteres'}), 400

    results = search_chapters(query)
    return jsonify({
        'query': query,
        'results': results,
        'total': len(results)
    })

if __name__ == '__main__':
    # Criar diretórios se não existirem
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/images', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
