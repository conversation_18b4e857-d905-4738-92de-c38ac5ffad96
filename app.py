from flask import Flask, render_template, jsonify
import os
from datetime import datetime

app = Flask(__name__)

# Configuração para servir arquivos estáticos
app.static_folder = 'static'
app.template_folder = 'templates'



@app.route('/')
def home():
    """Página inicial do CYPHER"""
    return render_template('index.html')

@app.route('/estudos')
def estudos():
    """Página principal de estudos com dashboard"""
    return render_template('estudos.html')



@app.route('/api/time')
def get_current_time():
    """API para obter horário atual"""
    return jsonify({
        'time': datetime.now().strftime('%H:%M:%S'),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/connection-status')
def get_connection_status():
    """API para simular status de conexão"""
    import random
    return jsonify({
        'connected': random.choice([True, False]),
        'status': 'CONNECTED' if random.choice([True, False]) else 'RECONNECTING...'
    })

# Dados do currículo de estudos
STUDY_CONTENT = [
    {
        "id": "fase1",
        "title": "[FASE 1: FUNDAMENTOS]",
        "icon": "shield",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "intro-security",
                "title": "1.1 Introdução à Segurança",
                "icon": "lock",
                "topics": ["Conceitos Fundamentais", "Tríade CIA", "Tipos de Ataques", "Frameworks de Segurança"]
            },
            {
                "id": "os-fundamentals",
                "title": "1.2 Fundamentos de SO",
                "icon": "terminal",
                "topics": ["Windows Fundamentals", "Linux Fundamentals", "Windows Avançado", "Linux Avançado"]
            },
            {
                "id": "network-fundamentals",
                "title": "1.3 Fundamentos de Redes",
                "icon": "network",
                "topics": ["Conceitos de Rede", "Protocolos Essenciais", "Análise de Tráfego", "Segurança de Rede"]
            },
            {
                "id": "programming",
                "title": "1.4 Programação para Segurança",
                "icon": "code",
                "topics": ["Python Fundamentals", "Python para Segurança", "Bash Scripting", "Git e Versionamento"]
            }
        ]
    },
    {
        "id": "fase2",
        "title": "[FASE 2: RECONHECIMENTO]",
        "icon": "search",
        "duration": "10-14 semanas",
        "items": [
            {
                "id": "osint",
                "title": "2.1 OSINT",
                "icon": "eye",
                "topics": ["Introdução ao OSINT", "Ferramentas Básicas", "OSINT Avançado", "OSINT Profissional"]
            },
            {
                "id": "vulnerability-analysis",
                "title": "2.2 Análise de Vulnerabilidades",
                "icon": "bug",
                "topics": ["Conceitos de Vulnerabilidade", "Ferramentas de Scanning", "Scanning Avançado", "Vulnerability Management"]
            },
            {
                "id": "social-engineering",
                "title": "2.3 Engenharia Social",
                "icon": "users",
                "topics": ["Psicologia da Segurança", "Tipos de Ataques", "Simulações Éticas", "Awareness e Treinamento"]
            }
        ]
    },
    {
        "id": "fase3",
        "title": "[FASE 3: WEB SECURITY]",
        "icon": "globe",
        "duration": "14-18 semanas",
        "items": [
            {
                "id": "web-dev",
                "title": "3.1 Desenvolvimento Web",
                "icon": "code",
                "topics": ["Tecnologias Web", "Arquitetura Web", "Desenvolvimento Backend"]
            },
            {
                "id": "owasp-top10",
                "title": "3.2 OWASP Top 10",
                "icon": "shield",
                "topics": ["OWASP Top 10 - Parte 1", "Ferramentas Básicas", "OWASP Top 10 - Parte 2", "Vulnerabilidades Avançadas"]
            },
            {
                "id": "apis-modern",
                "title": "3.3 APIs e Apps Modernas",
                "icon": "network",
                "topics": ["API Security", "Tecnologias Modernas"]
            }
        ]
    },
    {
        "id": "fase4",
        "title": "[FASE 4: INFRAESTRUTURA]",
        "icon": "server",
        "duration": "16-20 semanas",
        "items": [
            {
                "id": "network-security",
                "title": "4.1 Segurança de Redes",
                "icon": "network",
                "topics": ["Hardening de Rede", "Protocolos Seguros", "Ataques de Rede", "Técnicas Avançadas"]
            },
            {
                "id": "windows-security",
                "title": "4.2 Segurança Windows",
                "icon": "terminal",
                "topics": ["Hardening Windows", "Active Directory Basics", "Windows Security", "Advanced Windows Attacks"]
            },
            {
                "id": "linux-security",
                "title": "4.3 Segurança Linux",
                "icon": "terminal",
                "topics": ["Linux Hardening", "Monitoring e Logs", "Linux Security", "Advanced Linux Security"]
            }
        ]
    },
    {
        "id": "fase5",
        "title": "[FASE 5: CLOUD SECURITY]",
        "icon": "cloud",
        "duration": "12-16 semanas",
        "items": [
            {
                "id": "cloud-fundamentals",
                "title": "5.1 Cloud Computing",
                "icon": "cloud",
                "topics": ["Cloud Basics", "Cloud Security Fundamentals", "AWS Security", "Azure Security"]
            },
            {
                "id": "container-k8s",
                "title": "5.2 Container & Kubernetes",
                "icon": "server",
                "topics": ["Container Basics", "Container Security", "Kubernetes Security"]
            }
        ]
    },
    {
        "id": "fase6",
        "title": "[FASE 6: MOBILE SECURITY]",
        "icon": "smartphone",
        "duration": "10-14 semanas",
        "items": [
            {
                "id": "android-security",
                "title": "6.1 Android Security",
                "icon": "smartphone",
                "topics": ["Android Fundamentals", "Static Analysis", "Dynamic Analysis"]
            },
            {
                "id": "ios-security",
                "title": "6.2 iOS Security",
                "icon": "smartphone",
                "topics": ["iOS Fundamentals"]
            }
        ]
    },
    {
        "id": "fase7",
        "title": "[FASE 7: FERRAMENTAS]",
        "icon": "wrench",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "pentest-tools",
                "title": "7.1 Ferramentas de Pentest",
                "icon": "target",
                "topics": ["Kali Linux", "Metasploit Framework", "Automação"]
            },
            {
                "id": "siem-detection",
                "title": "7.2 SIEM e Detecção",
                "icon": "eye",
                "topics": ["SIEM Fundamentals", "Threat Hunting"]
            }
        ]
    },
    {
        "id": "fase8",
        "title": "[FASE 8: ESPECIALIZAÇÃO]",
        "icon": "microscope",
        "duration": "16-24 semanas",
        "items": [
            {
                "id": "malware-analysis",
                "title": "8.1 Malware Analysis",
                "icon": "bug",
                "topics": ["Static Analysis", "Dynamic Analysis", "Reverse Engineering"]
            },
            {
                "id": "digital-forensics",
                "title": "8.2 Digital Forensics",
                "icon": "search",
                "topics": ["Forensics Fundamentals", "Network Forensics"]
            },
            {
                "id": "exploit-dev",
                "title": "8.3 Exploit Development",
                "icon": "code",
                "topics": ["Assembly Language", "Buffer Overflows"]
            }
        ]
    },
    {
        "id": "fase9",
        "title": "[FASE 9: CERTIFICAÇÕES]",
        "icon": "award",
        "duration": "8-12 semanas",
        "items": [
            {
                "id": "certifications",
                "title": "9.1 Certificações Essenciais",
                "icon": "award",
                "topics": ["CompTIA Security+", "CEH (Certified Ethical Hacker)", "CISSP", "OSCP"]
            },
            {
                "id": "career-dev",
                "title": "9.2 Desenvolvimento de Carreira",
                "icon": "target",
                "topics": ["Soft Skills", "Portfolio Development"]
            }
        ]
    }
]

@app.route('/api/study-content')
def get_study_content():
    """API para obter conteúdo de estudos"""
    return jsonify(STUDY_CONTENT)



if __name__ == '__main__':
    # Criar diretórios se não existirem
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/images', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
