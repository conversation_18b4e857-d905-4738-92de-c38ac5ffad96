"use client"

import { StudySidebar } from "@/components/study-sidebar"
import { Terminal, User, Wifi, WifiOff, Clock, Target, Award } from "lucide-react"
import { useEffect, useState } from "react"

export default function EstudosPage() {
  const [isConnected, setIsConnected] = useState(true)
  const [currentTime, setCurrentTime] = useState("")

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString())
    }, 1000)

    const connectionTimer = setInterval(() => {
      setIsConnected((prev) => !prev)
    }, 8000)

    return () => {
      clearInterval(timer)
      clearInterval(connectionTimer)
    }
  }, [])

  return (
    <div className="min-h-screen bg-black text-white relative">
      {/* Header */}
      <header className="border-b border-gray-800 p-4 bg-black/90 backdrop-blur-sm relative z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Terminal className="w-6 h-6 text-white" />
            <span className="text-xl font-bold">CYPHER</span>
            <span className="text-gray-500 font-mono">/ learning_protocol</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-gray-400 font-mono text-sm">
              {isConnected ? <Wifi className="w-4 h-4 text-white" /> : <WifiOff className="w-4 h-4 text-gray-600" />}
              <span>{isConnected ? "CONNECTED" : "RECONNECTING..."}</span>
            </div>
            <div className="text-gray-500 font-mono text-sm">{currentTime}</div>
            <div className="flex items-center gap-2 text-white font-mono">
              <User className="w-4 h-4" />
              <span>@student</span>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <StudySidebar />

        {/* Main Content */}
        <main className="flex-1 p-8 relative z-10">
          <div className="max-w-4xl">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-white mb-4">$ CYPHER_LEARNING_SYSTEM_INITIALIZED</h1>
              <div className="bg-gray-900 border border-gray-700 p-4 font-mono text-sm relative">
                <div className="absolute top-2 right-2 flex gap-1">
                  <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                  <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <div className="text-white">root@cypher:/home/<USER>/div>
                <div className="text-gray-300 text-sm mb-4 font-mono">
                  [INFO] Comprehensive cybersecurity curriculum loaded...
                </div>
                <div className="text-gray-300">[INFO] 9 phases, 200+ weeks of structured learning</div>
                <div className="text-gray-300">[WARNING] Dedication and practice required for mastery</div>
                <div className="text-white mt-2 animate-pulse">
                  {">"} SELECT PHASE FROM SIDEBAR TO BEGIN YOUR JOURNEY_
                </div>
              </div>
            </div>

            {/* Learning Path Overview */}
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                <div className="flex items-center gap-3 mb-3">
                  <Target className="w-6 h-6 text-white" />
                  <h3 className="text-lg font-bold text-white">[BEGINNER_PATH]</h3>
                </div>
                <p className="text-gray-300 text-sm mb-4 font-mono">
                  Start with fundamentals: IT basics, networking, and basic security concepts.
                </p>
                <div className="text-xs font-mono text-gray-500">PHASES: 1-3 | DURATION: ~40 weeks</div>
                <div className="mt-2 w-full bg-gray-800 h-1">
                  <div className="bg-white h-1 w-0"></div>
                </div>
              </div>

              <div className="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                <div className="flex items-center gap-3 mb-3">
                  <Clock className="w-6 h-6 text-white" />
                  <h3 className="text-lg font-bold text-white">[INTERMEDIATE_PATH]</h3>
                </div>
                <p className="text-gray-300 text-sm mb-4 font-mono">
                  Advanced topics: infrastructure security, cloud, mobile, and specialized tools.
                </p>
                <div className="text-xs font-mono text-gray-500">PHASES: 4-7 | DURATION: ~60 weeks</div>
                <div className="mt-2 w-full bg-gray-800 h-1">
                  <div className="bg-white h-1 w-0"></div>
                </div>
              </div>

              <div className="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                <div className="flex items-center gap-3 mb-3">
                  <Award className="w-6 h-6 text-white" />
                  <h3 className="text-lg font-bold text-white">[EXPERT_PATH]</h3>
                </div>
                <p className="text-gray-300 text-sm mb-4 font-mono">
                  Specialization: malware analysis, forensics, exploit development, and certifications.
                </p>
                <div className="text-xs font-mono text-gray-500">PHASES: 8-9 | DURATION: ~32 weeks</div>
                <div className="mt-2 w-full bg-gray-800 h-1">
                  <div className="bg-white h-1 w-0"></div>
                </div>
              </div>
            </div>

            {/* Phase Overview */}
            <div className="border border-gray-700 p-6 bg-gray-900/20 mb-8">
              <h3 className="text-white font-bold mb-4">[CURRICULUM_OVERVIEW]</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 font-mono text-sm">
                <div>
                  <div className="text-gray-400 mb-2">FOUNDATIONAL PHASES:</div>
                  <div className="text-white mb-1">• FASE 1: Fundamentos (8-12w)</div>
                  <div className="text-white mb-1">• FASE 2: Reconhecimento (10-14w)</div>
                  <div className="text-white">• FASE 3: Web Security (14-18w)</div>
                </div>
                <div>
                  <div className="text-gray-400 mb-2">ADVANCED PHASES:</div>
                  <div className="text-white mb-1">• FASE 4: Infraestrutura (16-20w)</div>
                  <div className="text-white mb-1">• FASE 5: Cloud Security (12-16w)</div>
                  <div className="text-white mb-1">• FASE 6: Mobile Security (10-14w)</div>
                  <div className="text-white">• FASE 7: Ferramentas (8-12w)</div>
                </div>
                <div>
                  <div className="text-gray-400 mb-2">SPECIALIZATION:</div>
                  <div className="text-white mb-1">• FASE 8: Especialização (16-24w)</div>
                  <div className="text-white">• FASE 9: Certificações (8-12w)</div>
                  <div className="text-gray-500 mt-3">TOTAL: ~200 weeks</div>
                  <div className="text-gray-500">COMMITMENT: 3-4 years</div>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="border border-gray-700 p-4 bg-gray-900/20">
              <h3 className="text-white font-bold mb-3">[LEARNING_SYSTEM_STATUS]</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 font-mono text-sm">
                <div>
                  <div className="text-gray-400">MODULES:</div>
                  <div className="text-white">200+ topics</div>
                </div>
                <div>
                  <div className="text-gray-400">PHASES:</div>
                  <div className="text-white">9 complete</div>
                </div>
                <div>
                  <div className="text-gray-400">DIFFICULTY:</div>
                  <div className="text-white">PROGRESSIVE</div>
                </div>
                <div>
                  <div className="text-gray-400">STATUS:</div>
                  <div className="text-white">READY_TO_START</div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
