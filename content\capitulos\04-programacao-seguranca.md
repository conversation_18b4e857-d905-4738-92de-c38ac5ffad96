# 1.4 Programação para Segurança

> **📍 Localização no Currículo CYPHER**  
> **FASE 1: FUNDAMENTOS** → **Módulo 1.4: Programação para Segurança**  
> Duração: 8-12 semanas | Pré-requisitos: Todos os módulos anteriores da FASE 1

## Objetivos do Capítulo

Ao final deste capítulo, você será capaz de:
- Desenvolver scripts Python para automação de segurança
- Criar scripts Bash para administração de sistemas
- Usar Git para versionamento e colaboração
- Aplicar boas práticas de desenvolvimento seguro
- Preparar-se para **desenvolvimento de exploits** na FASE 8
- Finalizar a **FASE 1: FUNDAMENTOS** com base sólida

## Introdução à Programação para Segurança

A programação é uma habilidade essencial para profissionais de cibersegurança, permitindo:

- 🤖 **Automação** de tarefas repetitivas
- 🔍 **Análise** de grandes volumes de dados
- 🛠️ **Desenvolvimento** de ferramentas personalizadas
- 🎯 **Exploração** de vulnerabilidades (ético)
- 📊 **Relatórios** automatizados

### Por que Python?

Python é a linguagem preferida em cibersegurança por:
- Sintaxe simples e legível
- Vasta biblioteca de segurança
- Integração com ferramentas existentes
- Comunidade ativa de segurança

## Python Fundamentals

### Configuração do Ambiente

```bash
# Instalar Python (Ubuntu/Debian)
sudo apt update
sudo apt install python3 python3-pip python3-venv

# Criar ambiente virtual
python3 -m venv cypher-env
source cypher-env/bin/activate

# Instalar bibliotecas essenciais
pip install requests scapy python-nmap
```

### Conceitos Básicos

```python
#!/usr/bin/env python3
"""
CYPHER Security Script Template
Módulo: 1.4 Programação para Segurança
"""

import sys
import argparse
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Função principal do script"""
    parser = argparse.ArgumentParser(description='CYPHER Security Tool')
    parser.add_argument('target', help='Target IP or hostname')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logging.info(f"Iniciando análise de {args.target}")
    
    # Sua lógica aqui
    
if __name__ == "__main__":
    main()
```

## Python para Segurança

### 1. Scanner de Portas Simples

```python
import socket
import threading
from datetime import datetime

def scan_port(target, port):
    """Escaneia uma porta específica"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((target, port))
        sock.close()
        
        if result == 0:
            print(f"Porta {port}: ABERTA")
            return True
    except socket.gaierror:
        pass
    return False

def port_scanner(target, start_port=1, end_port=1000):
    """Scanner de portas multi-threaded"""
    print(f"Escaneando {target} de {start_port} a {end_port}")
    print(f"Iniciado em: {datetime.now()}")
    print("-" * 50)
    
    open_ports = []
    
    def scan_thread(port):
        if scan_port(target, port):
            open_ports.append(port)
    
    threads = []
    for port in range(start_port, end_port + 1):
        thread = threading.Thread(target=scan_thread, args=(port,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"\nPortas abertas: {sorted(open_ports)}")

# Uso
if __name__ == "__main__":
    target = "127.0.0.1"
    port_scanner(target, 1, 100)
```

### 2. Análise de Logs

```python
import re
import collections
from datetime import datetime

def analyze_apache_logs(log_file):
    """Analisa logs do Apache para detectar padrões suspeitos"""
    
    # Padrões suspeitos
    suspicious_patterns = [
        r'\.\./',           # Directory traversal
        r'<script',         # XSS attempts
        r'union.*select',   # SQL injection
        r'cmd\.exe',        # Command injection
        r'/etc/passwd',     # File inclusion
    ]
    
    ip_counter = collections.Counter()
    suspicious_requests = []
    
    with open(log_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            # Extrair IP
            ip_match = re.match(r'^(\d+\.\d+\.\d+\.\d+)', line)
            if ip_match:
                ip = ip_match.group(1)
                ip_counter[ip] += 1
            
            # Verificar padrões suspeitos
            for pattern in suspicious_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    suspicious_requests.append({
                        'line': line_num,
                        'ip': ip if ip_match else 'Unknown',
                        'pattern': pattern,
                        'request': line.strip()
                    })
    
    # Relatório
    print("=== ANÁLISE DE LOGS DE SEGURANÇA ===")
    print(f"Top 10 IPs mais ativos:")
    for ip, count in ip_counter.most_common(10):
        print(f"  {ip}: {count} requests")
    
    print(f"\nRequests suspeitos encontrados: {len(suspicious_requests)}")
    for req in suspicious_requests[:10]:  # Mostrar apenas os primeiros 10
        print(f"  Linha {req['line']}: {req['ip']} - {req['pattern']}")

# Uso
# analyze_apache_logs('/var/log/apache2/access.log')
```

### 3. Hash e Verificação de Integridade

```python
import hashlib
import os

def calculate_file_hash(filepath, algorithm='sha256'):
    """Calcula hash de um arquivo"""
    hash_obj = hashlib.new(algorithm)
    
    with open(filepath, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_obj.update(chunk)
    
    return hash_obj.hexdigest()

def verify_file_integrity(filepath, expected_hash, algorithm='sha256'):
    """Verifica integridade de um arquivo"""
    actual_hash = calculate_file_hash(filepath, algorithm)
    
    if actual_hash == expected_hash:
        print(f"✅ Integridade verificada: {filepath}")
        return True
    else:
        print(f"❌ Integridade comprometida: {filepath}")
        print(f"   Esperado: {expected_hash}")
        print(f"   Atual:    {actual_hash}")
        return False

def monitor_directory(directory):
    """Monitora integridade de arquivos em um diretório"""
    baseline = {}
    
    print(f"Criando baseline para {directory}")
    for root, dirs, files in os.walk(directory):
        for file in files:
            filepath = os.path.join(root, file)
            baseline[filepath] = calculate_file_hash(filepath)
    
    print(f"Baseline criado com {len(baseline)} arquivos")
    return baseline

# Uso
# baseline = monitor_directory('/etc')
# verify_file_integrity('/etc/passwd', baseline['/etc/passwd'])
```

## Bash Scripting para Segurança

### Script de Hardening Básico

```bash
#!/bin/bash
# CYPHER Security Hardening Script
# Módulo: 1.4 Programação para Segurança

set -euo pipefail  # Modo strict

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função de log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se é root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "Este script deve ser executado como root"
        exit 1
    fi
}

# Atualizar sistema
update_system() {
    log "Atualizando sistema..."
    apt update && apt upgrade -y
    apt autoremove -y
}

# Configurar firewall básico
setup_firewall() {
    log "Configurando firewall..."
    
    # Instalar ufw se não estiver instalado
    if ! command -v ufw &> /dev/null; then
        apt install -y ufw
    fi
    
    # Configuração básica
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    
    # Permitir SSH (cuidado!)
    ufw allow ssh
    
    # Ativar firewall
    ufw --force enable
    
    log "Firewall configurado"
}

# Configurar SSH seguro
secure_ssh() {
    log "Configurando SSH seguro..."
    
    # Backup da configuração original
    cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
    
    # Aplicar configurações seguras
    sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
    sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/' /etc/ssh/sshd_config
    
    # Reiniciar SSH
    systemctl restart ssh
    
    warning "SSH configurado para usar apenas chaves públicas"
    warning "Certifique-se de ter uma chave configurada antes de desconectar!"
}

# Função principal
main() {
    log "Iniciando hardening básico do sistema"
    
    check_root
    update_system
    setup_firewall
    secure_ssh
    
    log "Hardening básico concluído!"
    log "Reinicie o sistema para garantir que todas as mudanças sejam aplicadas"
}

# Executar apenas se chamado diretamente
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

## Git para Versionamento

### Configuração Inicial

```bash
# Configurar Git
git config --global user.name "Seu Nome"
git config --global user.email "<EMAIL>"

# Criar repositório para scripts de segurança
mkdir cypher-scripts
cd cypher-scripts
git init

# Criar estrutura
mkdir -p {scanners,analyzers,hardening,exploits}
touch README.md .gitignore

# .gitignore para projetos de segurança
cat > .gitignore << EOF
# Arquivos sensíveis
*.key
*.pem
passwords.txt
secrets/

# Logs
*.log
logs/

# Resultados de scans
results/
reports/

# Python
__pycache__/
*.pyc
*.pyo
venv/

# Temporários
*.tmp
*.temp
EOF
```

### Workflow de Desenvolvimento Seguro

```bash
# 1. Criar branch para nova feature
git checkout -b feature/port-scanner

# 2. Desenvolver e testar
# ... código ...

# 3. Commit com mensagem descritiva
git add .
git commit -m "feat: adicionar scanner de portas multi-threaded

- Implementar scanning paralelo
- Adicionar detecção de serviços
- Incluir rate limiting para evitar detecção"

# 4. Push para repositório
git push origin feature/port-scanner

# 5. Merge após revisão
git checkout main
git merge feature/port-scanner
git push origin main
```

## Conexões com o Currículo CYPHER

### 🔗 Aplicação dos Módulos Anteriores

Este módulo integra todos os conhecimentos da FASE 1:
- **[1.1 Introdução à Segurança](/capitulos/01-introducao-seguranca)** - Frameworks aplicados em código
- **[1.2 Fundamentos de SO](/capitulos/02-fundamentos-so)** - Scripts para Windows/Linux
- **[1.3 Fundamentos de Redes](/capitulos/03-fundamentos-redes)** - Automação de análise de rede

### 🚀 Preparação para Fases Avançadas

#### FASE 2 - RECONHECIMENTO:
- **2.1 OSINT** - Scripts para coleta automatizada
- **2.2 Análise de Vulnerabilidades** - Automação de scanning

#### FASE 3 - WEB SECURITY:
- **3.1 Desenvolvimento Web** - Código seguro
- **3.2 OWASP Top 10** - Scripts de teste

#### FASE 7 - FERRAMENTAS:
- **7.1 Ferramentas de Pentest** - Customização e automação

#### FASE 8 - ESPECIALIZAÇÃO:
- **8.3 Exploit Development** - Base para desenvolvimento de exploits

### 📋 Checklist Final da FASE 1

Antes de avançar para a FASE 2, certifique-se de que você:

- [ ] Domina Python básico para segurança
- [ ] Sabe criar scripts Bash para automação
- [ ] Compreende Git e versionamento
- [ ] Consegue integrar conhecimentos de SO e redes
- [ ] Aplicou todos os conceitos da Tríade CIA
- [ ] Completou projetos práticos

## Próximos Passos

### 🎉 Parabéns! FASE 1 Concluída

Você completou todos os módulos da **FASE 1: FUNDAMENTOS**:
- ✅ 1.1 Introdução à Segurança
- ✅ 1.2 Fundamentos de SO  
- ✅ 1.3 Fundamentos de Redes
- ✅ 1.4 Programação para Segurança

### 🚀 Próxima Fase: RECONHECIMENTO

Agora você está preparado para a **FASE 2: RECONHECIMENTO** (10-14 semanas):

#### 2.1 OSINT
- Técnicas de reconhecimento passivo
- Ferramentas especializadas
- Automação com Python

#### 2.2 Análise de Vulnerabilidades  
- Scanning avançado
- Identificação de vulnerabilidades
- Relatórios automatizados

#### 2.3 Engenharia Social
- Psicologia da segurança
- Simulações éticas
- Awareness training

---

**📊 Progresso Geral**: FASE 1 COMPLETA (1/9 fases)  
**⏱️ Tempo estimado de estudo**: 6-8 horas  
**🎯 Nível de dificuldade**: ⭐⭐⭐⭐☆  
**🔗 Módulo ID**: `programming` (CYPHER Database)  
**🏆 Conquista Desbloqueada**: "Fundamentos Sólidos" 🛡️
