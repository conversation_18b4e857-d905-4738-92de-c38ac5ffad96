// CYPHER Learning System - Estudos Page JavaScript

class EstudosPage {
    constructor() {
        this.studyContent = [];
        this.openSections = [];
        this.openSubSections = [];
        this.init();
    }

    async init() {
        await this.loadStudyContent();
        this.renderSidebar();
        this.setupEventListeners();
    }

    async loadStudyContent() {
        try {
            const response = await fetch('/api/study-content');
            this.studyContent = await response.json();
        } catch (error) {
            console.error('Error loading study content:', error);
            // Fallback content
            this.studyContent = this.getFallbackContent();
        }
    }

    getFallbackContent() {
        return [
            {
                id: "fase1",
                title: "[FASE 1: FUNDAMENTOS]",
                icon: "shield",
                duration: "8-12 semanas",
                items: [
                    {
                        id: "intro-security",
                        title: "1.1 Introdução à Segurança",
                        icon: "lock",
                        topics: ["Conceitos Fundamentais", "Tríade CIA", "Tipos de Ataques", "Frameworks de Segurança"]
                    },
                    {
                        id: "os-fundamentals",
                        title: "1.2 Fundamentos de SO",
                        icon: "terminal",
                        topics: ["Windows Fundamentals", "Linux Fundamentals", "Windows Avançado", "Linux Avançado"]
                    }
                ]
            }
        ];
    }

    renderSidebar() {
        const sidebarNav = document.getElementById('sidebar-nav');
        if (!sidebarNav) return;

        sidebarNav.innerHTML = '';

        this.studyContent.forEach(phase => {
            const phaseElement = this.createPhaseElement(phase);
            sidebarNav.appendChild(phaseElement);
        });

        // Reinitialize icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    createPhaseElement(phase) {
        const isPhaseOpen = this.openSections.includes(phase.id);
        
        const phaseDiv = document.createElement('div');
        phaseDiv.className = 'mb-1';

        const triggerButton = document.createElement('button');
        triggerButton.className = 'w-full justify-start p-3 h-auto font-mono text-left hover:bg-gray-800 hover:text-white transition-all flex items-center gap-3';
        triggerButton.setAttribute('data-collapsible-trigger', '');
        triggerButton.setAttribute('data-target', `phase-content-${phase.id}`);
        triggerButton.setAttribute('aria-expanded', isPhaseOpen.toString());

        triggerButton.innerHTML = `
            <i data-lucide="${phase.icon}" class="w-4 h-4 text-white shrink-0"></i>
            <div class="flex-1">
                <div class="text-sm font-bold">${phase.title}</div>
                <div class="text-xs text-gray-500">${phase.duration}</div>
            </div>
            <i data-lucide="${isPhaseOpen ? 'chevron-down' : 'chevron-right'}" data-chevron class="w-4 h-4 text-gray-500"></i>
        `;

        const contentDiv = document.createElement('div');
        contentDiv.id = `phase-content-${phase.id}`;
        contentDiv.className = 'ml-4 border-l border-gray-800';
        contentDiv.style.display = isPhaseOpen ? 'block' : 'none';

        phase.items.forEach(module => {
            const moduleElement = this.createModuleElement(module);
            contentDiv.appendChild(moduleElement);
        });

        phaseDiv.appendChild(triggerButton);
        phaseDiv.appendChild(contentDiv);

        return phaseDiv;
    }

    createModuleElement(module) {
        const isModuleOpen = this.openSubSections.includes(module.id);
        
        const moduleDiv = document.createElement('div');

        const triggerButton = document.createElement('button');
        triggerButton.className = 'w-full justify-start p-2 pl-6 h-auto font-mono text-left text-gray-300 hover:bg-gray-800 hover:text-white transition-all flex items-center gap-2';
        triggerButton.setAttribute('data-collapsible-trigger', '');
        triggerButton.setAttribute('data-target', `module-content-${module.id}`);
        triggerButton.setAttribute('aria-expanded', isModuleOpen.toString());

        triggerButton.innerHTML = `
            <i data-lucide="${module.icon}" class="w-3 h-3 text-white shrink-0"></i>
            <span class="flex-1 text-xs">${module.title}</span>
            <i data-lucide="${isModuleOpen ? 'chevron-down' : 'chevron-right'}" data-chevron class="w-3 h-3 text-gray-500"></i>
        `;

        const contentDiv = document.createElement('div');
        contentDiv.id = `module-content-${module.id}`;
        contentDiv.className = 'ml-6 border-l border-gray-700';
        contentDiv.style.display = isModuleOpen ? 'block' : 'none';

        module.topics.forEach(topic => {
            const topicButton = document.createElement('button');
            topicButton.className = 'w-full justify-start p-2 pl-4 h-auto font-mono text-left text-gray-400 hover:bg-gray-800 hover:text-white transition-all flex items-center gap-2';
            
            topicButton.innerHTML = `
                <div class="w-1 h-1 bg-white rounded-full opacity-60"></div>
                <span class="text-xs">${topic}</span>
            `;

            contentDiv.appendChild(topicButton);
        });

        moduleDiv.appendChild(triggerButton);
        moduleDiv.appendChild(contentDiv);

        return moduleDiv;
    }

    setupEventListeners() {
        // Setup collapsible functionality
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-collapsible-trigger]');
            if (trigger) {
                e.preventDefault();
                this.handleCollapsibleClick(trigger);
            }
        });
    }

    handleCollapsibleClick(trigger) {
        const targetId = trigger.getAttribute('data-target');
        const content = document.getElementById(targetId);
        const chevron = trigger.querySelector('[data-chevron]');
        
        if (content) {
            const isOpen = content.style.display !== 'none';
            
            if (isOpen) {
                content.style.display = 'none';
                trigger.setAttribute('aria-expanded', 'false');
                if (chevron) {
                    chevron.setAttribute('data-lucide', 'chevron-right');
                }
                
                // Update state
                if (targetId.startsWith('phase-content-')) {
                    const phaseId = targetId.replace('phase-content-', '');
                    this.openSections = this.openSections.filter(id => id !== phaseId);
                } else if (targetId.startsWith('module-content-')) {
                    const moduleId = targetId.replace('module-content-', '');
                    this.openSubSections = this.openSubSections.filter(id => id !== moduleId);
                }
            } else {
                content.style.display = 'block';
                trigger.setAttribute('aria-expanded', 'true');
                if (chevron) {
                    chevron.setAttribute('data-lucide', 'chevron-down');
                }
                
                // Update state
                if (targetId.startsWith('phase-content-')) {
                    const phaseId = targetId.replace('phase-content-', '');
                    if (!this.openSections.includes(phaseId)) {
                        this.openSections.push(phaseId);
                    }
                } else if (targetId.startsWith('module-content-')) {
                    const moduleId = targetId.replace('module-content-', '');
                    if (!this.openSubSections.includes(moduleId)) {
                        this.openSubSections.push(moduleId);
                    }
                }
            }
            
            // Reinitialize icons after changing them
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }
}

// Initialize the estudos page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EstudosPage();
});
