// CYPHER Learning System - Enhanced Estudos Page JavaScript

class EstudosPage {
    constructor() {
        this.studyContent = [];
        this.openSections = [];
        this.openSubSections = [];
        this.sessionStartTime = Date.now();
        this.progressData = this.loadProgress();
        this.init();
    }

    async init() {
        await this.loadStudyContent();
        this.renderSidebar();
        this.setupEventListeners();
        this.startSessionTimer();
        this.updateProgressStats();
        this.initializeAnimations();
    }

    // Load progress from localStorage
    loadProgress() {
        const saved = localStorage.getItem('cypher-progress');
        return saved ? JSON.parse(saved) : {
            completedModules: [],
            currentPhase: null,
            totalTimeSpent: 0,
            lastAccessed: null
        };
    }

    // Save progress to localStorage
    saveProgress() {
        localStorage.setItem('cypher-progress', JSON.stringify(this.progressData));
    }

    async loadStudyContent() {
        try {
            const response = await fetch('/api/study-content');
            this.studyContent = await response.json();
        } catch (error) {
            console.error('Error loading study content:', error);
            // Fallback content
            this.studyContent = this.getFallbackContent();
        }
    }

    getFallbackContent() {
        return [
            {
                id: "fase1",
                title: "[FASE 1: FUNDAMENTOS]",
                icon: "shield",
                duration: "8-12 semanas",
                items: [
                    {
                        id: "intro-security",
                        title: "1.1 Introdução à Segurança",
                        icon: "lock",
                        topics: ["Conceitos Fundamentais", "Tríade CIA", "Tipos de Ataques", "Frameworks de Segurança"]
                    },
                    {
                        id: "os-fundamentals",
                        title: "1.2 Fundamentos de SO",
                        icon: "terminal",
                        topics: ["Windows Fundamentals", "Linux Fundamentals", "Windows Avançado", "Linux Avançado"]
                    }
                ]
            }
        ];
    }

    renderSidebar() {
        const sidebarNav = document.getElementById('sidebar-nav');
        if (!sidebarNav) return;

        sidebarNav.innerHTML = '';

        this.studyContent.forEach(phase => {
            const phaseElement = this.createPhaseElement(phase);
            sidebarNav.appendChild(phaseElement);
        });

        // Reinitialize icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    createPhaseElement(phase) {
        const isPhaseOpen = this.openSections.includes(phase.id);

        const phaseDiv = document.createElement('div');
        phaseDiv.className = 'mb-2';

        const triggerButton = document.createElement('button');
        triggerButton.className = 'sidebar-phase-button w-full p-3 rounded-lg font-mono text-left flex items-center gap-3 group';
        triggerButton.style.cssText = `
            background: var(--card);
            border: 1px solid var(--border);
            color: var(--foreground);
        `;
        triggerButton.setAttribute('data-collapsible-trigger', '');
        triggerButton.setAttribute('data-target', `phase-content-${phase.id}`);
        triggerButton.setAttribute('aria-expanded', isPhaseOpen.toString());

        // Add hover effect
        triggerButton.addEventListener('mouseenter', () => {
            triggerButton.style.background = 'var(--secondary)';
            triggerButton.style.borderColor = 'rgba(255, 255, 255, 0.5)';
        });
        triggerButton.addEventListener('mouseleave', () => {
            triggerButton.style.background = 'var(--card)';
            triggerButton.style.borderColor = 'var(--border)';
        });

        triggerButton.innerHTML = `
            <div class="phase-icon-container flex items-center justify-center w-8 h-8 rounded-lg">
                <i data-lucide="${phase.icon}" class="w-4 h-4 text-white"></i>
            </div>
            <div class="flex-1">
                <div class="text-sm font-bold text-primary">${phase.title}</div>
                <div class="text-xs text-muted">${phase.duration}</div>
            </div>
            <i data-lucide="${isPhaseOpen ? 'chevron-down' : 'chevron-right'}" data-chevron class="chevron-icon w-4 h-4 text-white ${isPhaseOpen ? 'expanded' : ''}"></i>
        `;

        const contentDiv = document.createElement('div');
        contentDiv.id = `phase-content-${phase.id}`;
        contentDiv.className = 'ml-2 mt-2 space-y-1';
        contentDiv.style.display = isPhaseOpen ? 'block' : 'none';

        phase.items.forEach(module => {
            const moduleElement = this.createModuleElement(module);
            contentDiv.appendChild(moduleElement);
        });

        phaseDiv.appendChild(triggerButton);
        phaseDiv.appendChild(contentDiv);

        return phaseDiv;
    }

    createModuleElement(module) {
        const isModuleOpen = this.openSubSections.includes(module.id);

        const moduleDiv = document.createElement('div');
        moduleDiv.className = 'mb-1';

        const triggerButton = document.createElement('button');
        triggerButton.className = 'sidebar-module-button w-full p-2 pl-4 rounded-md font-mono text-left flex items-center gap-3';
        triggerButton.style.cssText = `
            background: var(--muted);
            border: 1px solid transparent;
            color: var(--secondary-foreground);
        `;
        triggerButton.setAttribute('data-collapsible-trigger', '');
        triggerButton.setAttribute('data-target', `module-content-${module.id}`);
        triggerButton.setAttribute('aria-expanded', isModuleOpen.toString());

        // Add hover effect
        triggerButton.addEventListener('mouseenter', () => {
            triggerButton.style.background = 'var(--secondary)';
            triggerButton.style.borderColor = 'var(--border-light)';
            triggerButton.style.color = 'var(--foreground)';
        });
        triggerButton.addEventListener('mouseleave', () => {
            triggerButton.style.background = 'var(--muted)';
            triggerButton.style.borderColor = 'transparent';
            triggerButton.style.color = 'var(--secondary-foreground)';
        });

        triggerButton.innerHTML = `
            <div class="module-icon-container flex items-center justify-center w-6 h-6 rounded">
                <i data-lucide="${module.icon}" class="w-3 h-3 text-white"></i>
            </div>
            <span class="flex-1 text-xs font-medium">${module.title}</span>
            <i data-lucide="${isModuleOpen ? 'chevron-down' : 'chevron-right'}" data-chevron class="chevron-icon w-3 h-3 text-muted ${isModuleOpen ? 'expanded' : ''}"></i>
        `;

        const contentDiv = document.createElement('div');
        contentDiv.id = `module-content-${module.id}`;
        contentDiv.className = 'ml-6 mt-1 space-y-1';
        contentDiv.style.display = isModuleOpen ? 'block' : 'none';

        module.topics.forEach(topic => {
            const topicButton = document.createElement('button');
            topicButton.className = 'sidebar-topic-button w-full p-2 pl-4 rounded font-mono text-left flex items-center gap-2';
            topicButton.style.cssText = `
                background: transparent;
                color: var(--muted-foreground);
                border: 1px solid transparent;
            `;

            // Add hover effect for topics
            topicButton.addEventListener('mouseenter', () => {
                topicButton.style.background = 'var(--border-light)';
                topicButton.style.color = 'var(--foreground)';
                topicButton.style.borderColor = 'var(--border)';
            });
            topicButton.addEventListener('mouseleave', () => {
                topicButton.style.background = 'transparent';
                topicButton.style.color = 'var(--muted-foreground)';
                topicButton.style.borderColor = 'transparent';
            });

            // Add click handler to load markdown content
            topicButton.addEventListener('click', () => {
                // Generate markdown file path based on topic
                const topicSlug = topic.toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[^\w\-]/g, '');

                const filepath = `fase1/introducao-seguranca/${topicSlug}.md`;
                this.loadMarkdownContent(filepath, topic);
            });

            topicButton.innerHTML = `
                <div class="topic-indicator w-2 h-2 rounded-full"></div>
                <span class="text-xs">${topic}</span>
            `;

            contentDiv.appendChild(topicButton);
        });

        moduleDiv.appendChild(triggerButton);
        moduleDiv.appendChild(contentDiv);

        return moduleDiv;
    }

    setupEventListeners() {
        // Setup collapsible functionality
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-collapsible-trigger]');
            if (trigger) {
                e.preventDefault();
                this.handleCollapsibleClick(trigger);
            }
        });
    }

    handleCollapsibleClick(trigger) {
        const targetId = trigger.getAttribute('data-target');
        const content = document.getElementById(targetId);
        const chevron = trigger.querySelector('[data-chevron]');

        if (content) {
            const isOpen = content.style.display !== 'none';

            if (isOpen) {
                content.style.display = 'none';
                trigger.setAttribute('aria-expanded', 'false');
                if (chevron) {
                    chevron.setAttribute('data-lucide', 'chevron-right');
                    chevron.classList.remove('expanded');
                }

                // Update state
                if (targetId.startsWith('phase-content-')) {
                    const phaseId = targetId.replace('phase-content-', '');
                    this.openSections = this.openSections.filter(id => id !== phaseId);
                } else if (targetId.startsWith('module-content-')) {
                    const moduleId = targetId.replace('module-content-', '');
                    this.openSubSections = this.openSubSections.filter(id => id !== moduleId);
                }
            } else {
                content.style.display = 'block';
                trigger.setAttribute('aria-expanded', 'true');
                if (chevron) {
                    chevron.setAttribute('data-lucide', 'chevron-down');
                    chevron.classList.add('expanded');
                }

                // Update state
                if (targetId.startsWith('phase-content-')) {
                    const phaseId = targetId.replace('phase-content-', '');
                    if (!this.openSections.includes(phaseId)) {
                        this.openSections.push(phaseId);
                    }
                } else if (targetId.startsWith('module-content-')) {
                    const moduleId = targetId.replace('module-content-', '');
                    if (!this.openSubSections.includes(moduleId)) {
                        this.openSubSections.push(moduleId);
                    }
                }
            }
            
            // Reinitialize icons after changing them
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    // Session timer
    startSessionTimer() {
        const updateTimer = () => {
            const elapsed = Date.now() - this.sessionStartTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);

            const timerElement = document.getElementById('session-time');
            if (timerElement) {
                timerElement.textContent = `Session: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        };

        updateTimer();
        setInterval(updateTimer, 1000);
    }

    // Update progress statistics
    updateProgressStats() {
        const totalModules = 78;
        const completed = this.progressData.completedModules.length;
        const percentage = Math.round((completed / totalModules) * 100);

        // Update progress bar in sidebar
        const progressBar = document.querySelector('.w-full.h-2.rounded-full div');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }

        // Update progress text
        const progressText = document.querySelector('.mt-2.text-xs.font-mono.text-muted');
        if (progressText) {
            progressText.textContent = `${completed}/${totalModules} modules completed`;
        }
    }

    // Initialize animations
    initializeAnimations() {
        // Add stagger animation to cards
        const cards = document.querySelectorAll('.cyber-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-fade-in');
        });
    }

    // Show notification
    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-primary text-background px-4 py-2 rounded-lg font-mono text-sm z-50';
        notification.style.animation = 'slideInRight 0.3s ease-out';
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Load markdown content
    async loadMarkdownContent(filepath, title) {
        try {
            const response = await fetch(`/markdown/${filepath}`);
            const data = await response.json();

            if (response.ok) {
                // Show content area and hide dashboard
                document.getElementById('dashboard-area').style.display = 'none';
                document.getElementById('content-area').style.display = 'block';

                // Update content
                document.getElementById('content-title').textContent = title;
                document.getElementById('content-path').textContent = filepath;
                document.getElementById('markdown-content').innerHTML = data.content;

                // Setup back button
                document.getElementById('back-to-dashboard').onclick = () => {
                    this.showDashboard();
                };

                // Reinitialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }

                this.showNotification(`Carregado: ${title}`);
            } else {
                this.showNotification(`Erro: ${data.error}`);
            }
        } catch (error) {
            this.showNotification(`Erro ao carregar conteúdo: ${error.message}`);
        }
    }

    // Show dashboard
    showDashboard() {
        document.getElementById('content-area').style.display = 'none';
        document.getElementById('dashboard-area').style.display = 'block';
    }
}

// Initialize the estudos page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EstudosPage();
});
