// CYPHER Capítulos - JavaScript

class CapitulosPage {
    constructor() {
        this.searchTimeout = null;
        this.currentTheme = localStorage.getItem('cypher-theme') || 'dark';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSearch();
        this.setupTheme();
        this.loadBookmarks();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Search input
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.clearSearch();
                }
            });
        }
    }

    setupSearch() {
        // Focus search with Ctrl+K
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }

    setupTheme() {
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme(this.currentTheme);
        this.updateThemeIcon();
        localStorage.setItem('cypher-theme', this.currentTheme);
    }

    applyTheme(theme) {
        const html = document.documentElement;
        if (theme === 'light') {
            html.classList.remove('dark');
            html.classList.add('light');
            document.body.style.backgroundColor = '#ffffff';
            document.body.style.color = '#111827';
        } else {
            html.classList.remove('light');
            html.classList.add('dark');
            document.body.style.backgroundColor = '#000000';
            document.body.style.color = '#ffffff';
        }
    }

    updateThemeIcon() {
        const themeToggle = document.getElementById('theme-toggle');
        const icon = themeToggle?.querySelector('i');
        if (icon) {
            icon.setAttribute('data-lucide', this.currentTheme === 'dark' ? 'sun' : 'moon');
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        
        if (query.length < 3) {
            this.clearSearch();
            return;
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    async performSearch(query) {
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (response.ok) {
                this.displaySearchResults(data);
            } else {
                console.error('Search error:', data.error);
            }
        } catch (error) {
            console.error('Search failed:', error);
        }
    }

    displaySearchResults(data) {
        const defaultContent = document.getElementById('default-content');
        const searchResults = document.getElementById('search-results');
        const searchResultsContent = document.getElementById('search-results-content');

        if (!searchResults || !searchResultsContent) return;

        defaultContent.classList.add('hidden');
        searchResults.classList.remove('hidden');

        if (data.results.length === 0) {
            searchResultsContent.innerHTML = `
                <div class="text-center py-8">
                    <i data-lucide="search-x" class="w-12 h-12 text-gray-500 mx-auto mb-4"></i>
                    <p class="text-gray-400">Nenhum resultado encontrado para "${data.query}"</p>
                </div>
            `;
        } else {
            let html = `<div class="mb-4 text-sm text-gray-400">
                ${data.total} resultado(s) encontrado(s) para "${data.query}"
            </div>`;

            data.results.forEach(result => {
                html += `
                    <div class="border border-gray-700 p-4 mb-4 bg-gray-900/30 rounded-lg">
                        <h3 class="text-lg font-bold text-white mb-2">
                            <a href="/capitulos/${result.chapter.id}" class="hover:text-green-400 transition-colors">
                                ${result.chapter.title}
                            </a>
                        </h3>
                        <div class="space-y-2">
                `;

                result.matches.forEach(match => {
                    html += `
                        <div class="text-sm">
                            <div class="text-gray-500 font-mono">Linha ${match.line_number}:</div>
                            <div class="bg-gray-800 p-2 rounded text-gray-300 font-mono text-xs">
                                ${this.highlightSearchTerm(match.context, data.query)}
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                        <div class="mt-3">
                            <a href="/capitulos/${result.chapter.id}" 
                               class="text-green-400 hover:text-green-300 text-sm">
                                Ver capítulo completo →
                            </a>
                        </div>
                    </div>
                `;
            });

            searchResultsContent.innerHTML = html;
        }

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    highlightSearchTerm(text, term) {
        const regex = new RegExp(`(${term})`, 'gi');
        return text.replace(regex, '<mark class="bg-yellow-400 text-black px-1 rounded">$1</mark>');
    }

    clearSearch() {
        const searchInput = document.getElementById('search-input');
        const defaultContent = document.getElementById('default-content');
        const searchResults = document.getElementById('search-results');

        if (searchInput) searchInput.value = '';
        if (defaultContent) defaultContent.classList.remove('hidden');
        if (searchResults) searchResults.classList.add('hidden');
    }

    loadBookmarks() {
        const bookmarks = JSON.parse(localStorage.getItem('cypher-bookmarks') || '[]');
        this.updateBookmarkIcons(bookmarks);
    }

    updateBookmarkIcons(bookmarks) {
        // Update bookmark icons in the chapter cards
        bookmarks.forEach(chapterId => {
            const chapterCard = document.querySelector(`[href="/capitulos/${chapterId}"]`);
            if (chapterCard) {
                // Add bookmark indicator
                const bookmarkIcon = document.createElement('i');
                bookmarkIcon.setAttribute('data-lucide', 'bookmark');
                bookmarkIcon.className = 'w-4 h-4 text-yellow-400 absolute top-2 right-2';
                chapterCard.style.position = 'relative';
                chapterCard.appendChild(bookmarkIcon);
            }
        });

        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }
}

// Global functions for quick actions
function startRandomChapter() {
    const chapterLinks = document.querySelectorAll('a[href^="/capitulos/"]');
    if (chapterLinks.length > 0) {
        const randomIndex = Math.floor(Math.random() * chapterLinks.length);
        const randomLink = chapterLinks[randomIndex];
        window.location.href = randomLink.href;
    }
}

function toggleBookmarks() {
    const bookmarks = JSON.parse(localStorage.getItem('cypher-bookmarks') || '[]');
    
    if (bookmarks.length === 0) {
        alert('Você ainda não tem capítulos favoritos. Visite um capítulo e clique no ícone de bookmark para adicioná-lo aos favoritos.');
        return;
    }

    // Filter chapters to show only bookmarked ones
    const allChapters = document.querySelectorAll('#chapters-nav a');
    const showingBookmarks = document.body.dataset.showingBookmarks === 'true';

    if (showingBookmarks) {
        // Show all chapters
        allChapters.forEach(chapter => {
            chapter.style.display = 'block';
        });
        document.body.dataset.showingBookmarks = 'false';
    } else {
        // Show only bookmarked chapters
        allChapters.forEach(chapter => {
            const chapterId = chapter.href.split('/').pop();
            if (bookmarks.includes(chapterId)) {
                chapter.style.display = 'block';
            } else {
                chapter.style.display = 'none';
            }
        });
        document.body.dataset.showingBookmarks = 'true';
    }
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    new CapitulosPage();
});
