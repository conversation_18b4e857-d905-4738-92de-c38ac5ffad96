{% extends "base.html" %}

{% block title %}CYPHER - Learning Protocol{% endblock %}
{% block description %}Sistema de aprendizado de cibersegurança CYPHER{% endblock %}

{% block content %}
<div class="min-h-screen relative" style="background: var(--background); color: var(--foreground);">
    <!-- Header -->
    <header class="p-4 backdrop-blur-sm relative z-10" style="border-bottom: 1px solid var(--border); background: var(--background);">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <!-- Back Button -->
                <a href="/" class="flex items-center gap-2 px-3 py-2 rounded-lg transition-all hover:bg-secondary" style="border: 1px solid var(--border);">
                    <i data-lucide="arrow-left" class="w-4 h-4"></i>
                    <span class="font-mono text-sm">HOME</span>
                </a>

                <div class="flex items-center gap-2">
                    <i data-lucide="terminal" class="w-6 h-6 text-primary"></i>
                    <span class="text-xl font-bold">CYPHER</span>
                    <span class="text-muted font-mono">/ learning_protocol</span>
                </div>
            </div>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2 text-muted font-mono text-sm">
                    <i id="connection-icon" data-lucide="wifi" class="w-4 h-4"></i>
                    <span id="connection-status">CONNECTED</span>
                </div>
                <div id="current-time" class="text-muted font-mono text-sm"></div>
                <div class="flex items-center gap-2 font-mono">
                    <i data-lucide="user" class="w-4 h-4 text-accent"></i>
                    <span>@student</span>
                </div>
            </div>
        </div>
    </header>

    <div class="flex h-screen">
        <!-- Professional Sidebar -->
        <aside id="study-sidebar" class="w-80 h-full overflow-y-auto relative" style="background: var(--sidebar); border-right: 1px solid var(--border);">
            <!-- Sidebar Header -->
            <div class="p-4 relative z-10" style="border-bottom: 1px solid var(--border);">
                <div class="flex items-center gap-2 mb-3">
                    <i data-lucide="layers" class="w-5 h-5 text-accent"></i>
                    <h2 class="text-lg font-bold text-primary">CYPHER_MODULES</h2>
                </div>
                <p class="text-xs text-muted font-mono">root@cypher:/learning#</p>
                <div class="mt-3 flex items-center gap-2">
                    <div class="w-2 h-2 rounded-full" style="background: var(--accent);"></div>
                    <span class="text-xs text-muted font-mono">SYSTEM_ACTIVE</span>
                </div>

                <!-- Progress Overview -->
                <div class="mt-4 p-3 rounded-lg" style="background: var(--card); border: 1px solid var(--border-light);">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-xs font-mono text-muted">OVERALL PROGRESS</span>
                        <span class="text-xs font-mono text-primary">0%</span>
                    </div>
                    <div class="w-full h-2 rounded-full" style="background: var(--muted);">
                        <div class="h-2 rounded-full transition-all duration-500" style="background: var(--accent); width: 0%;"></div>
                    </div>
                    <div class="mt-2 text-xs font-mono text-muted">0/78 modules completed</div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="p-2 relative z-10" id="sidebar-nav">
                <!-- Sidebar content will be populated by JavaScript -->
            </nav>

            <!-- Professional Footer -->
            <div class="p-4 mt-auto relative z-10" style="border-top: 1px solid var(--border);">
                <div class="text-xs font-mono text-muted space-y-2">
                    <div class="flex items-center gap-2">
                        <i data-lucide="user" class="w-3 h-3 text-muted"></i>
                        <span>cyber_student@cypher</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i data-lucide="clock" class="w-3 h-3 text-muted"></i>
                        <span id="session-time">Session: 00:00:00</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <i data-lucide="activity" class="w-3 h-3 text-accent"></i>
                        <span class="text-secondary-foreground">STATUS: READY</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Professional Main Content -->
        <main class="flex-1 h-full overflow-y-auto relative">
            <!-- Content Area -->
            <div id="content-area" class="hidden p-6">
                <div class="max-w-4xl mx-auto">
                    <!-- Content Header -->
                    <div class="mb-6 pb-4" style="border-bottom: 1px solid var(--border);">
                        <button id="back-to-dashboard" class="flex items-center gap-2 px-3 py-2 rounded-lg transition-all hover:bg-secondary mb-4" style="border: 1px solid var(--border);">
                            <i data-lucide="arrow-left" class="w-4 h-4"></i>
                            <span class="font-mono text-sm">VOLTAR AO DASHBOARD</span>
                        </button>
                        <h1 id="content-title" class="text-2xl font-bold text-primary mb-2"></h1>
                        <p id="content-path" class="text-sm text-muted font-mono"></p>
                    </div>

                    <!-- Markdown Content -->
                    <div id="markdown-content" class="prose prose-invert max-w-none">
                        <!-- Conteúdo markdown será carregado aqui -->
                    </div>
                </div>
            </div>

            <!-- Dashboard Area -->
            <div id="dashboard-area" class="p-6 space-y-6">
                <!-- Welcome Header -->
                <div class="mb-8">
                    <div class="flex items-center gap-3 mb-4">
                        <i data-lucide="terminal" class="w-8 h-8 text-accent"></i>
                        <h1 class="text-3xl font-bold text-primary">CYPHER_LEARNING_DASHBOARD</h1>
                    </div>

                    <!-- Professional Terminal -->
                    <div class="p-6 font-mono text-sm relative rounded-lg" style="background: var(--card); border: 1px solid var(--border);">
                        <div class="absolute top-3 right-3 flex gap-1">
                            <div class="w-2 h-2 rounded-full" style="background: var(--destructive);"></div>
                            <div class="w-2 h-2 rounded-full" style="background: var(--warning);"></div>
                            <div class="w-2 h-2 rounded-full" style="background: var(--accent);"></div>
                        </div>
                        <div class="text-primary mb-2">root@cypher:/home/<USER>/div>
                        <div class="text-secondary-foreground mb-3">
                            [INFO] Comprehensive cybersecurity curriculum loaded...
                        </div>
                        <div class="text-secondary-foreground">[INFO] 9 phases • 78 modules • 200+ weeks of learning</div>
                        <div class="text-accent">[WARNING] Dedication and practice required for mastery</div>
                        <div class="mt-4 flex items-center gap-2">
                            <span class="text-muted">$</span>
                            <span class="text-primary">SELECT_PHASE_TO_BEGIN_JOURNEY</span>
                        </div>
                    </div>
                </div>

                <!-- Stats Dashboard -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                    <div class="p-4 rounded-lg transition-all hover:bg-secondary" style="background: var(--card); border: 1px solid var(--border);">
                        <div class="flex items-center gap-3 mb-2">
                            <i data-lucide="layers" class="w-5 h-5 text-accent"></i>
                            <span class="text-sm font-mono text-muted">PHASES</span>
                        </div>
                        <div class="text-2xl font-bold text-primary">9</div>
                        <div class="text-xs text-muted font-mono">Complete curriculum</div>
                    </div>

                    <div class="p-4 rounded-lg transition-all hover:bg-secondary" style="background: var(--card); border: 1px solid var(--border);">
                        <div class="flex items-center gap-3 mb-2">
                            <i data-lucide="book-open" class="w-5 h-5 text-accent"></i>
                            <span class="text-sm font-mono text-muted">MODULES</span>
                        </div>
                        <div class="text-2xl font-bold text-primary">78</div>
                        <div class="text-xs text-muted font-mono">Learning modules</div>
                    </div>

                    <div class="p-4 rounded-lg transition-all hover:bg-secondary" style="background: var(--card); border: 1px solid var(--border);">
                        <div class="flex items-center gap-3 mb-2">
                            <i data-lucide="clock" class="w-5 h-5 text-accent"></i>
                            <span class="text-sm font-mono text-muted">DURATION</span>
                        </div>
                        <div class="text-2xl font-bold text-primary">200+</div>
                        <div class="text-xs text-muted font-mono">Weeks total</div>
                    </div>

                    <div class="p-4 rounded-lg transition-all hover:bg-secondary" style="background: var(--card); border: 1px solid var(--border);">
                        <div class="flex items-center gap-3 mb-2">
                            <i data-lucide="target" class="w-5 h-5 text-accent"></i>
                            <span class="text-sm font-mono text-muted">PROGRESS</span>
                        </div>
                        <div class="text-2xl font-bold text-primary">0%</div>
                        <div class="text-xs text-muted font-mono">Completed</div>
                    </div>
                </div>

                <!-- Enhanced Learning Paths -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold mb-6 flex items-center gap-2">
                        <i data-lucide="map" class="w-5 h-5 text-primary"></i>
                        <span class="neon-text">LEARNING_PATHS</span>
                    </h2>

                    <div class="grid md:grid-cols-3 gap-6">
                        <!-- Beginner Path -->
                        <div class="cyber-card p-6 hover-glow group cursor-pointer">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="p-2 rounded-lg" style="background: var(--primary-glow);">
                                    <i data-lucide="play-circle" class="w-6 h-6 text-primary"></i>
                                </div>
                                <h3 class="text-lg font-bold text-primary">[BEGINNER_PATH]</h3>
                            </div>
                            <p class="text-secondary text-sm mb-4 font-mono leading-relaxed">
                                Start your cybersecurity journey with fundamentals: IT basics, networking, and core security concepts.
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">PHASES:</span>
                                    <span class="text-accent">1-3</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DURATION:</span>
                                    <span class="text-accent">~40 weeks</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DIFFICULTY:</span>
                                    <span class="text-success">BEGINNER</span>
                                </div>
                            </div>
                            <div class="w-full h-2 rounded-full" style="background: var(--muted);">
                                <div class="h-2 rounded-full transition-all duration-500 group-hover:w-1/4" style="background: var(--gradient-primary); width: 0%;"></div>
                            </div>
                        </div>

                        <!-- Intermediate Path -->
                        <div class="cyber-card p-6 hover-glow group cursor-pointer">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="p-2 rounded-lg" style="background: var(--accent-glow);">
                                    <i data-lucide="zap" class="w-6 h-6 text-accent"></i>
                                </div>
                                <h3 class="text-lg font-bold text-accent">[INTERMEDIATE_PATH]</h3>
                            </div>
                            <p class="text-secondary text-sm mb-4 font-mono leading-relaxed">
                                Advanced topics: infrastructure security, cloud platforms, mobile security, and specialized tools.
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">PHASES:</span>
                                    <span class="text-accent">4-7</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DURATION:</span>
                                    <span class="text-accent">~60 weeks</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DIFFICULTY:</span>
                                    <span class="text-warning">INTERMEDIATE</span>
                                </div>
                            </div>
                            <div class="w-full h-2 rounded-full" style="background: var(--muted);">
                                <div class="h-2 rounded-full transition-all duration-500 group-hover:w-1/4" style="background: var(--accent); width: 0%;"></div>
                            </div>
                        </div>

                        <!-- Expert Path -->
                        <div class="cyber-card p-6 hover-glow group cursor-pointer">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="p-2 rounded-lg" style="background: rgba(255, 68, 68, 0.2);">
                                    <i data-lucide="crown" class="w-6 h-6 text-destructive"></i>
                                </div>
                                <h3 class="text-lg font-bold text-destructive">[EXPERT_PATH]</h3>
                            </div>
                            <p class="text-secondary text-sm mb-4 font-mono leading-relaxed">
                                Specialization: malware analysis, digital forensics, exploit development, and industry certifications.
                            </p>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">PHASES:</span>
                                    <span class="text-accent">8-9</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DURATION:</span>
                                    <span class="text-accent">~32 weeks</span>
                                </div>
                                <div class="flex justify-between text-xs font-mono">
                                    <span class="text-muted">DIFFICULTY:</span>
                                    <span class="text-destructive">EXPERT</span>
                                </div>
                            </div>
                            <div class="w-full h-2 rounded-full" style="background: var(--muted);">
                                <div class="h-2 rounded-full transition-all duration-500 group-hover:w-1/4" style="background: var(--destructive); width: 0%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Curriculum Overview -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold mb-6 flex items-center gap-2">
                        <i data-lucide="layers" class="w-5 h-5 text-primary"></i>
                        <span class="neon-text">CURRICULUM_OVERVIEW</span>
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Foundational Phases -->
                        <div class="cyber-card p-6">
                            <div class="flex items-center gap-2 mb-4">
                                <i data-lucide="foundation" class="w-5 h-5 text-success"></i>
                                <h3 class="font-bold text-success">FOUNDATIONAL</h3>
                            </div>
                            <div class="space-y-3 font-mono text-sm">
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-primary">FASE 1: Fundamentos</span>
                                    <span class="text-muted">8-12w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-primary">FASE 2: Reconhecimento</span>
                                    <span class="text-muted">10-14w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-primary">FASE 3: Web Security</span>
                                    <span class="text-muted">14-18w</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-3" style="border-top: 1px solid var(--border);">
                                <div class="text-xs text-muted">TOTAL: 32-44 weeks</div>
                            </div>
                        </div>

                        <!-- Advanced Phases -->
                        <div class="cyber-card p-6">
                            <div class="flex items-center gap-2 mb-4">
                                <i data-lucide="trending-up" class="w-5 h-5 text-warning"></i>
                                <h3 class="font-bold text-warning">ADVANCED</h3>
                            </div>
                            <div class="space-y-3 font-mono text-sm">
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-accent">FASE 4: Infraestrutura</span>
                                    <span class="text-muted">16-20w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-accent">FASE 5: Cloud Security</span>
                                    <span class="text-muted">12-16w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-accent">FASE 6: Mobile Security</span>
                                    <span class="text-muted">10-14w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-accent">FASE 7: Ferramentas</span>
                                    <span class="text-muted">8-12w</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-3" style="border-top: 1px solid var(--border);">
                                <div class="text-xs text-muted">TOTAL: 46-62 weeks</div>
                            </div>
                        </div>

                        <!-- Specialization -->
                        <div class="cyber-card p-6">
                            <div class="flex items-center gap-2 mb-4">
                                <i data-lucide="star" class="w-5 h-5 text-destructive"></i>
                                <h3 class="font-bold text-destructive">SPECIALIZATION</h3>
                            </div>
                            <div class="space-y-3 font-mono text-sm">
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-destructive">FASE 8: Especialização</span>
                                    <span class="text-muted">16-24w</span>
                                </div>
                                <div class="flex items-center justify-between p-2 rounded" style="background: var(--muted);">
                                    <span class="text-destructive">FASE 9: Certificações</span>
                                    <span class="text-muted">8-12w</span>
                                </div>
                            </div>
                            <div class="mt-4 pt-3" style="border-top: 1px solid var(--border);">
                                <div class="text-xs text-muted">TOTAL: 24-36 weeks</div>
                            </div>

                            <!-- Summary -->
                            <div class="mt-6 p-3 rounded-lg" style="background: var(--gradient-primary); color: var(--background);">
                                <div class="font-mono text-sm font-bold">GRAND TOTAL</div>
                                <div class="font-mono text-lg">~200 weeks</div>
                                <div class="font-mono text-xs opacity-80">3-4 years commitment</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced System Status -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- System Status -->
                    <div class="cyber-card p-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i data-lucide="activity" class="w-5 h-5 text-primary pulse-glow"></i>
                            <h3 class="font-bold neon-text">SYSTEM_STATUS</h3>
                        </div>
                        <div class="space-y-4 font-mono text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-muted">LEARNING_ENGINE:</span>
                                <span class="text-success flex items-center gap-1">
                                    <div class="w-2 h-2 rounded-full bg-success pulse-glow"></div>
                                    ONLINE
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-muted">NEURAL_LINK:</span>
                                <span class="text-primary flex items-center gap-1">
                                    <div class="w-2 h-2 rounded-full bg-primary pulse-glow"></div>
                                    ACTIVE
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-muted">DIFFICULTY_SCALING:</span>
                                <span class="text-accent">PROGRESSIVE</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-muted">STUDENT_STATUS:</span>
                                <span class="text-warning">READY_TO_LEARN</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="cyber-card p-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i data-lucide="zap" class="w-5 h-5 text-accent"></i>
                            <h3 class="font-bold text-accent">QUICK_ACTIONS</h3>
                        </div>
                        <div class="space-y-3">
                            <button class="btn-primary w-full justify-center cyber-border">
                                <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                                START FASE 1
                            </button>
                            <button class="btn-secondary w-full justify-center cyber-border">
                                <i data-lucide="bookmark" class="w-4 h-4 mr-2"></i>
                                BOOKMARK PROGRESS
                            </button>
                            <button class="btn-secondary w-full justify-center cyber-border">
                                <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                                LEARNING PREFERENCES
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Footer Info -->
                <div class="mt-8 p-4 rounded-lg text-center" style="background: var(--muted); border: 1px solid var(--border);">
                    <div class="font-mono text-sm text-muted">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <i data-lucide="shield" class="w-4 h-4 text-primary"></i>
                            <span>CYPHER Learning System v2.0.0</span>
                        </div>
                        <div class="text-xs">
                            Select a phase from the sidebar to begin your cybersecurity journey
                        </div>
                    </div>
                </div>
            </div> <!-- End dashboard-area -->
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/estudos.js') }}"></script>
{% endblock %}
