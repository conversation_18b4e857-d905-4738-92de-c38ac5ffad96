{% extends "base.html" %}

{% block title %}CYPHER - Learning Protocol{% endblock %}
{% block description %}Sistema de aprendizado de cibersegurança CYPHER{% endblock %}

{% block content %}
<div class="min-h-screen bg-black text-white relative">
    <!-- Header -->
    <header class="border-b border-gray-800 p-4 bg-black/90 backdrop-blur-sm relative z-10">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <i data-lucide="terminal" class="w-6 h-6 text-white"></i>
                <span class="text-xl font-bold">CYPHER</span>
                <span class="text-gray-500 font-mono">/ learning_protocol</span>
            </div>
            <div class="flex items-center gap-4">
                <a href="/capitulos"
                   class="flex items-center gap-2 px-3 py-1 bg-gray-900 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors text-sm">
                    <i data-lucide="book-open" class="w-4 h-4"></i>
                    <span>Capítulos</span>
                </a>
                <div class="flex items-center gap-2 text-gray-400 font-mono text-sm">
                    <i id="connection-icon" data-lucide="wifi" class="w-4 h-4 text-white"></i>
                    <span id="connection-status">CONNECTED</span>
                </div>
                <div id="current-time" class="text-gray-500 font-mono text-sm"></div>
                <div class="flex items-center gap-2 text-white font-mono">
                    <i data-lucide="user" class="w-4 h-4"></i>
                    <span>@student</span>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside id="study-sidebar" class="w-80 border-r border-gray-800 bg-black h-screen overflow-y-auto relative">
            <div class="p-4 border-b border-gray-800 relative z-10">
                <h2 class="text-lg font-bold text-white">CYPHER_MODULES</h2>
                <p class="text-xs text-gray-500 font-mono mt-1">root@cypher:/learning#</p>
                <div class="mt-2 flex items-center gap-2">
                    <div class="w-2 h-2 bg-white rounded-full"></div>
                    <span class="text-xs text-gray-400 font-mono">NEURAL_LINK_ACTIVE</span>
                </div>
            </div>

            <nav class="p-2 relative z-10" id="sidebar-nav">
                <!-- Sidebar content will be populated by JavaScript -->
            </nav>

            <!-- Footer da sidebar -->
            <div class="p-4 border-t border-gray-800 mt-auto relative z-10">
                <div class="text-xs font-mono text-gray-500">
                    <div class="flex items-center gap-2 mb-2">
                        <i data-lucide="skull" class="w-3 h-3"></i>
                        <span>LEARNING_MODE: ACTIVE</span>
                    </div>
                    <div>$ whoami</div>
                    <div class="text-white">cyber_student</div>
                    <div class="mt-2">$ progress</div>
                    <div>phase_1: initializing...</div>
                    <div class="mt-2 text-white">
                        <i data-lucide="zap" class="w-3 h-3 inline mr-1"></i>
                        STATUS: READY_TO_LEARN
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8 relative z-10">
            <div class="max-w-4xl">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-white mb-4">$ CYPHER_LEARNING_SYSTEM_INITIALIZED</h1>
                    <div class="bg-gray-900 border border-gray-700 p-4 font-mono text-sm relative">
                        <div class="absolute top-2 right-2 flex gap-1">
                            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
                            <div class="w-2 h-2 bg-gray-600 rounded-full"></div>
                            <div class="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                        <div class="text-white">root@cypher:/home/<USER>/div>
                        <div class="text-gray-300 text-sm mb-4 font-mono">
                            [INFO] Comprehensive cybersecurity curriculum loaded...
                        </div>
                        <div class="text-gray-300">[INFO] 9 phases, 200+ weeks of structured learning</div>
                        <div class="text-gray-300">[WARNING] Dedication and practice required for mastery</div>
                        <div class="text-white mt-2 animate-pulse">
                            {">"} SELECT PHASE FROM SIDEBAR TO BEGIN YOUR JOURNEY_
                        </div>
                    </div>
                </div>

                <!-- Learning Path Overview -->
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                    <div class="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                        <div class="flex items-center gap-3 mb-3">
                            <i data-lucide="target" class="w-6 h-6 text-white"></i>
                            <h3 class="text-lg font-bold text-white">[BEGINNER_PATH]</h3>
                        </div>
                        <p class="text-gray-300 text-sm mb-4 font-mono">
                            Start with fundamentals: IT basics, networking, and basic security concepts.
                        </p>
                        <div class="text-xs font-mono text-gray-500">PHASES: 1-3 | DURATION: ~40 weeks</div>
                        <div class="mt-3 flex items-center justify-between">
                            <a href="/capitulos/01-introducao-seguranca"
                               class="text-green-400 hover:text-green-300 text-xs flex items-center gap-1">
                                <i data-lucide="book-open" class="w-3 h-3"></i>
                                Capítulos FASE 1
                            </a>
                            <div class="text-xs text-gray-600">0% completo</div>
                        </div>
                        <div class="mt-2 w-full bg-gray-800 h-1">
                            <div class="bg-white h-1 w-0"></div>
                        </div>
                    </div>

                    <div class="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                        <div class="flex items-center gap-3 mb-3">
                            <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                            <h3 class="text-lg font-bold text-white">[INTERMEDIATE_PATH]</h3>
                        </div>
                        <p class="text-gray-300 text-sm mb-4 font-mono">
                            Advanced topics: infrastructure security, cloud, mobile, and specialized tools.
                        </p>
                        <div class="text-xs font-mono text-gray-500">PHASES: 4-7 | DURATION: ~60 weeks</div>
                        <div class="mt-3 flex items-center justify-between">
                            <a href="/capitulos/05-osint-introducao"
                               class="text-green-400 hover:text-green-300 text-xs flex items-center gap-1">
                                <i data-lucide="book-open" class="w-3 h-3"></i>
                                Capítulos FASE 2-3
                            </a>
                            <div class="text-xs text-gray-600">0% completo</div>
                        </div>
                        <div class="mt-2 w-full bg-gray-800 h-1">
                            <div class="bg-white h-1 w-0"></div>
                        </div>
                    </div>

                    <div class="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all">
                        <div class="flex items-center gap-3 mb-3">
                            <i data-lucide="award" class="w-6 h-6 text-white"></i>
                            <h3 class="text-lg font-bold text-white">[EXPERT_PATH]</h3>
                        </div>
                        <p class="text-gray-300 text-sm mb-4 font-mono">
                            Specialization: malware analysis, forensics, exploit development, and certifications.
                        </p>
                        <div class="text-xs font-mono text-gray-500">PHASES: 8-9 | DURATION: ~32 weeks</div>
                        <div class="mt-3 flex items-center justify-between">
                            <a href="/capitulos"
                               class="text-green-400 hover:text-green-300 text-xs flex items-center gap-1">
                                <i data-lucide="book-open" class="w-3 h-3"></i>
                                Ver Todos os Capítulos
                            </a>
                            <div class="text-xs text-gray-600">0% completo</div>
                        </div>
                        <div class="mt-2 w-full bg-gray-800 h-1">
                            <div class="bg-white h-1 w-0"></div>
                        </div>
                    </div>
                </div>

                <!-- Phase Overview -->
                <div class="border border-gray-700 p-6 bg-gray-900/20 mb-8">
                    <h3 class="text-white font-bold mb-4">[CURRICULUM_OVERVIEW]</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 font-mono text-sm">
                        <div>
                            <div class="text-gray-400 mb-2">FOUNDATIONAL PHASES:</div>
                            <div class="text-white mb-1">• FASE 1: Fundamentos (8-12w)</div>
                            <div class="text-white mb-1">• FASE 2: Reconhecimento (10-14w)</div>
                            <div class="text-white">• FASE 3: Web Security (14-18w)</div>
                        </div>
                        <div>
                            <div class="text-gray-400 mb-2">ADVANCED PHASES:</div>
                            <div class="text-white mb-1">• FASE 4: Infraestrutura (16-20w)</div>
                            <div class="text-white mb-1">• FASE 5: Cloud Security (12-16w)</div>
                            <div class="text-white mb-1">• FASE 6: Mobile Security (10-14w)</div>
                            <div class="text-white">• FASE 7: Ferramentas (8-12w)</div>
                        </div>
                        <div>
                            <div class="text-gray-400 mb-2">SPECIALIZATION:</div>
                            <div class="text-white mb-1">• FASE 8: Especialização (16-24w)</div>
                            <div class="text-white">• FASE 9: Certificações (8-12w)</div>
                            <div class="text-gray-500 mt-3">TOTAL: ~200 weeks</div>
                            <div class="text-gray-500">COMMITMENT: 3-4 years</div>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="border border-gray-700 p-4 bg-gray-900/20">
                    <h3 class="text-white font-bold mb-3">[LEARNING_SYSTEM_STATUS]</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 font-mono text-sm">
                        <div>
                            <div class="text-gray-400">MODULES:</div>
                            <div class="text-white">200+ topics</div>
                        </div>
                        <div>
                            <div class="text-gray-400">PHASES:</div>
                            <div class="text-white">9 complete</div>
                        </div>
                        <div>
                            <div class="text-gray-400">DIFFICULTY:</div>
                            <div class="text-white">PROGRESSIVE</div>
                        </div>
                        <div>
                            <div class="text-gray-400">STATUS:</div>
                            <div class="text-white">READY_TO_START</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/estudos.js') }}"></script>
{% endblock %}
