{% extends "base.html" %}

{% block title %}CYPHER - Cybersecurity Learning Protocol{% endblock %}
{% block description %}Sistema avançado de aprendizado em cibersegurança com 9 fases estruturadas{% endblock %}

{% block content %}
<div class="min-h-screen relative overflow-hidden" style="background: var(--background);">
    <!-- Background Matrix Effect -->
    <div class="absolute inset-0 opacity-10">
        <div class="matrix-bg"></div>
    </div>

    <!-- Cyberpunk Grid Background -->
    <div class="absolute inset-0 opacity-5 circuit-bg"></div>

    <!-- Matrix Rain Effect -->
    <div class="matrix-rain"></div>

    <!-- Scan Lines -->
    <div class="absolute inset-0 scan-lines"></div>

    <!-- Main Content -->
    <div class="relative z-10 flex flex-col items-center justify-center min-h-screen p-8">
        <!-- CYPHER Logo/Title -->
        <div class="text-center mb-12">
            <div class="flex items-center justify-center gap-3 mb-4">
                <i data-lucide="terminal" class="w-12 h-12 text-primary pulse-glow"></i>
                <h1 class="text-6xl font-bold font-mono tracking-wider holographic" data-text="CYPHER">CYPHER</h1>
            </div>
            <div class="text-muted font-mono text-lg mb-2">/ cybersecurity_learning_protocol</div>
            <div class="text-muted font-mono text-sm opacity-70">v2.0.0 - Enhanced Dark Edition</div>
        </div>

        <!-- Terminal Window -->
        <div class="cyber-border scan-lines p-6 font-mono text-sm max-w-2xl w-full mb-8" style="background: var(--card); border-color: var(--border-accent);">
            <div class="flex items-center gap-2 mb-4 pb-2" style="border-bottom: 1px solid var(--border);">
                <div class="w-3 h-3 rounded-full pulse-glow" style="background: var(--destructive);"></div>
                <div class="w-3 h-3 rounded-full pulse-glow" style="background: var(--warning);"></div>
                <div class="w-3 h-3 rounded-full pulse-glow" style="background: var(--success);"></div>
                <span class="text-muted ml-2">root@cypher:~#</span>
            </div>
            <div class="space-y-2">
                <div class="text-primary neon-text">$ ./cypher --init</div>
                <div class="text-secondary">[INFO] Initializing CYPHER Learning System...</div>
                <div class="text-secondary">[INFO] Loading 9 cybersecurity phases...</div>
                <div class="text-secondary">[INFO] 78+ learning modules detected</div>
                <div class="text-primary neon-text">[SUCCESS] System ready for deployment</div>
                <div class="text-white mt-4">
                    <span class="text-muted">$</span>
                    <span class="terminal-cursor text-accent">access_learning_protocol</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 items-center">
            <a href="/estudos"
               class="btn-primary cyber-border group relative overflow-hidden px-8 py-4 transition-all hover:scale-105 pulse-glow">
                <div class="flex items-center gap-3">
                    <i data-lucide="play" class="w-5 h-5"></i>
                    <span class="font-mono font-semibold">INICIAR PROTOCOLO</span>
                </div>
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </a>

            <a href="#about"
               class="btn-secondary cyber-border transition-all hover:scale-105">
                <div class="flex items-center gap-3">
                    <i data-lucide="info" class="w-5 h-5"></i>
                    <span class="font-mono">SOBRE O SISTEMA</span>
                </div>
            </a>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 text-center">
            <div class="border border-gray-700 bg-gray-900/50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-white font-mono">9</div>
                <div class="text-gray-400 text-sm font-mono">FASES</div>
            </div>
            <div class="border border-gray-700 bg-gray-900/50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-white font-mono">200+</div>
                <div class="text-gray-400 text-sm font-mono">MÓDULOS</div>
            </div>
            <div class="border border-gray-700 bg-gray-900/50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-white font-mono">3-4</div>
                <div class="text-gray-400 text-sm font-mono">ANOS</div>
            </div>
            <div class="border border-gray-700 bg-gray-900/50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-white font-mono">∞</div>
                <div class="text-gray-400 text-sm font-mono">CONHECIMENTO</div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <section id="about" class="relative z-10 bg-gray-900/80 backdrop-blur-sm border-t border-gray-700 p-8">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-2xl font-bold text-white font-mono mb-6 text-center">[SOBRE_O_CYPHER]</h2>
            <div class="grid md:grid-cols-3 gap-6 text-sm">
                <div class="border border-gray-700 p-4 bg-gray-800/50">
                    <h3 class="text-white font-mono font-bold mb-2">MISSÃO</h3>
                    <p class="text-gray-300 font-mono">Formar especialistas em cibersegurança através de um protocolo estruturado e progressivo de aprendizado.</p>
                </div>
                <div class="border border-gray-700 p-4 bg-gray-800/50">
                    <h3 class="text-white font-mono font-bold mb-2">METODOLOGIA</h3>
                    <p class="text-gray-300 font-mono">9 fases sequenciais cobrindo desde fundamentos até especialização avançada em segurança.</p>
                </div>
                <div class="border border-gray-700 p-4 bg-gray-800/50">
                    <h3 class="text-white font-mono font-bold mb-2">TECNOLOGIA</h3>
                    <p class="text-gray-300 font-mono">Sistema desenvolvido em Python/Flask com interface responsiva e design cyberpunk.</p>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* CYPHER Home Page Styles */
.matrix-bg {
    background-image:
        linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px),
        linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    width: 100%;
    height: 100%;
    animation: matrix-scroll 20s linear infinite;
}

@keyframes matrix-scroll {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

.bg-red-500 { background-color: #ef4444; }
.bg-yellow-500 { background-color: #eab308; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-400 { background-color: #4ade80; }

/* Smooth scroll for anchor links */
html {
    scroll-behavior: smooth;
}

/* Glitch effect for title */
@keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
}

h1:hover {
    animation: glitch 0.3s ease-in-out;
}

/* Terminal cursor blink */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.animate-pulse {
    animation: blink 1s infinite;
}
</style>
{% endblock %}
