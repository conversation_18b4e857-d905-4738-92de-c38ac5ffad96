{% extends "base.html" %}

{% block title %}CYPHER - Create Next App{% endblock %}
{% block description %}Generated by create next app{% endblock %}

{% block content %}
<div class="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
    <main class="flex flex-col gap-8 row-start-2 items-center sm:items-start">
        <!-- Next.js Logo -->
        <img src="{{ url_for('static', filename='images/next-white.svg') }}" alt="Next.js logo" width="180" height="38">
        
        <ol class="list-inside list-decimal text-sm text-center sm:text-left font-mono">
            <li class="mb-2 tracking-[-0.01em]">
                Get started by editing 
                <code class="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-mono font-semibold">
                    app/page.tsx
                </code>
                .
            </li>
            <li class="tracking-[-0.01em]">
                Save and see your changes instantly.
            </li>
        </ol>

        <div class="flex gap-4 items-center flex-col sm:flex-row">
            <a class="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
               href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
               target="_blank"
               rel="noopener noreferrer">
                <img class="dark:invert" src="{{ url_for('static', filename='images/vercel.svg') }}" alt="Vercel logomark" width="20" height="20">
                Deploy now
            </a>
            <a class="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
               href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
               target="_blank"
               rel="noopener noreferrer">
                Read our docs
            </a>
        </div>
        
        <!-- CYPHER Link -->
        <div class="mt-8">
            <a href="/estudos" 
               class="rounded-full border border-solid border-gray-700 transition-colors flex items-center justify-center bg-gray-900 text-white gap-2 hover:bg-gray-800 font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5">
                <i data-lucide="terminal" class="w-4 h-4"></i>
                Enter CYPHER Learning System
            </a>
        </div>
    </main>
    
    <footer class="row-start-3 flex gap-6 flex-wrap items-center justify-center">
        <a class="flex items-center gap-2 hover:underline hover:underline-offset-4"
           href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
           target="_blank"
           rel="noopener noreferrer">
            <img aria-hidden src="{{ url_for('static', filename='images/file.svg') }}" alt="File icon" width="16" height="16">
            Learn
        </a>
        <a class="flex items-center gap-2 hover:underline hover:underline-offset-4"
           href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
           target="_blank"
           rel="noopener noreferrer">
            <img aria-hidden src="{{ url_for('static', filename='images/window.svg') }}" alt="Window icon" width="16" height="16">
            Examples
        </a>
        <a class="flex items-center gap-2 hover:underline hover:underline-offset-4"
           href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
           target="_blank"
           rel="noopener noreferrer">
            <img aria-hidden src="{{ url_for('static', filename='images/globe.svg') }}" alt="Globe icon" width="16" height="16">
            Go to nextjs.org →
        </a>
    </footer>
</div>

<style>
/* Custom styles for home page */
.bg-foreground {
    background-color: var(--foreground);
}

.text-background {
    color: var(--background);
}

.dark .bg-foreground {
    background-color: var(--foreground);
}

.dark .text-background {
    color: var(--background);
}

/* Invert filter for dark theme - already defined in main.css */

.dark\:hover\:bg-\[#ccc\]:hover {
    background-color: #cccccc;
}

.hover\:bg-\[#383838\]:hover {
    background-color: #383838;
}

.hover\:bg-\[#f2f2f2\]:hover {
    background-color: #f2f2f2;
}

.dark\:hover\:bg-\[#1a1a1a\]:hover {
    background-color: #1a1a1a;
}

.border-black\/\[\.08\] {
    border-color: rgba(0, 0, 0, 0.08);
}

.dark\:border-white\/\[\.145\] {
    border-color: rgba(255, 255, 255, 0.145);
}
</style>
{% endblock %}
