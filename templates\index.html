{% extends "base.html" %}

{% block title %}CYPHER - Cybersecurity Learning Protocol{% endblock %}
{% block description %}Comprehensive cybersecurity learning system with 9-phase curriculum{% endblock %}

{% block content %}
<div class="min-h-screen relative overflow-hidden" style="background: var(--background);">
    <!-- Matrix Rain Background -->
    <div class="absolute inset-0 opacity-10">
        <div class="matrix-rain"></div>
    </div>

    <!-- Top Navigation -->
    <nav class="relative z-10 p-6">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center gap-2 font-mono text-primary">
                <span class="text-white">></span>
                <span class="text-lg">@root</span>
            </div>
            <div class="flex items-center gap-6 font-mono text-sm">
                <a href="#about" class="text-muted hover:text-primary transition-colors">[ABOUT]</a>
                <a href="#contact" class="text-muted hover:text-primary transition-colors">[CONTACT]</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="relative z-10 flex flex-col items-center justify-center min-h-screen p-8 text-center page-content">
        <div class="w-full">
            <!-- Ultra Large Title -->
            <div class="mb-20">
                <div class="flex items-center justify-center gap-6 mb-12 title-container">
                    <span class="text-8xl md:text-[8rem] lg:text-[10rem] text-white font-mono prompt-symbol">></span>
                    <h1 class="text-[8rem] md:text-[16rem] lg:text-[20rem] xl:text-[24rem] font-bold font-mono tracking-wider text-primary cypher-title leading-none">
                        CYPHER
                    </h1>
                    <span class="text-8xl md:text-[8rem] lg:text-[10rem] text-white font-mono typing-cursor">_</span>
                </div>
            </div>

            <!-- CTA Button -->
            <div class="flex justify-center cta-container">
                <a href="/estudos" class="terminal-button px-16 py-6 rounded font-mono text-xl cta-button">
                    <span class="text-white">></span> [INITIATE_LEARNING] <span class="text-white">></span>
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}
