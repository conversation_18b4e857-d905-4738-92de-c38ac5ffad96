{% extends "base.html" %}

{% block title %}CYPHER - Capítulos de Estudo{% endblock %}
{% block description %}Lista completa de capítulos do sistema de aprendizado CYPHER{% endblock %}

{% block content %}
<div class="min-h-screen bg-black text-white">
    <!-- Header -->
    <header class="border-b border-gray-800 p-4 bg-black/90 backdrop-blur-sm">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <a href="/" class="flex items-center gap-2 hover:text-gray-300 transition-colors">
                    <i data-lucide="terminal" class="w-6 h-6 text-white"></i>
                    <span class="text-xl font-bold">CYPHER</span>
                </a>
                <span class="text-gray-500 font-mono">/ capítulos</span>
            </div>
            <div class="flex items-center gap-4">
                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg border border-gray-700 hover:bg-gray-800 transition-colors">
                    <i data-lucide="sun" class="w-4 h-4"></i>
                </button>
                <!-- Search -->
                <div class="relative">
                    <input type="text" 
                           id="search-input" 
                           placeholder="Buscar nos capítulos..." 
                           class="bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-sm w-64 focus:outline-none focus:border-gray-500">
                    <i data-lucide="search" class="absolute right-3 top-2.5 w-4 h-4 text-gray-500"></i>
                </div>
                <a href="/estudos" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4"></i>
                </a>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-80 border-r border-gray-800 bg-black h-screen overflow-y-auto">
            <div class="p-4 border-b border-gray-800">
                <h2 class="text-lg font-bold text-white">CAPÍTULOS</h2>
                <p class="text-xs text-gray-500 font-mono mt-1">{{ chapters|length }} capítulos disponíveis</p>
            </div>

            <nav class="p-2" id="chapters-nav">
                {% for chapter in chapters %}
                <a href="/capitulos/{{ chapter.id }}" 
                   class="block p-3 hover:bg-gray-800 hover:text-white transition-all rounded-lg mb-1 group">
                    <div class="flex items-center gap-3">
                        <i data-lucide="file-text" class="w-4 h-4 text-gray-500 group-hover:text-white"></i>
                        <div class="flex-1">
                            <div class="text-sm font-medium">{{ chapter.title }}</div>
                            <div class="text-xs text-gray-500 font-mono">{{ chapter.id }}</div>
                        </div>
                        <i data-lucide="chevron-right" class="w-4 h-4 text-gray-600 group-hover:text-gray-400"></i>
                    </div>
                </a>
                {% endfor %}
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="max-w-4xl">
                <!-- Search Results -->
                <div id="search-results" class="hidden mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">Resultados da Busca</h2>
                    <div id="search-results-content"></div>
                </div>

                <!-- Default Content -->
                <div id="default-content">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-white mb-4">📚 Biblioteca de Capítulos CYPHER</h1>
                        <div class="bg-gray-900 border border-gray-700 p-4 font-mono text-sm">
                            <div class="text-white">root@cypher:/capitulos#</div>
                            <div class="text-gray-300 text-sm mb-4 font-mono">
                                [INFO] Sistema de capítulos carregado com sucesso...
                            </div>
                            <div class="text-gray-300">[INFO] {{ chapters|length }} capítulos disponíveis para estudo</div>
                            <div class="text-gray-300">[INFO] Conteúdo em formato Markdown renderizado</div>
                            <div class="text-white mt-2">
                                {">"} Selecione um capítulo na barra lateral para começar_
                            </div>
                        </div>
                    </div>

                    <!-- Chapters Grid -->
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {% for chapter in chapters %}
                        <div class="border border-gray-700 p-6 bg-gray-900/30 hover:bg-gray-800/40 transition-all rounded-lg group">
                            <div class="flex items-start gap-3 mb-3">
                                <i data-lucide="book-open" class="w-6 h-6 text-white mt-1"></i>
                                <div class="flex-1">
                                    <h3 class="text-lg font-bold text-white group-hover:text-green-400 transition-colors">
                                        {{ chapter.title }}
                                    </h3>
                                    <p class="text-xs text-gray-500 font-mono mt-1">{{ chapter.id }}</p>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <p class="text-gray-300 text-sm">
                                    Capítulo de estudo em formato Markdown com exemplos práticos e exercícios.
                                </p>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="text-xs font-mono text-gray-500">
                                    <i data-lucide="clock" class="w-3 h-3 inline mr-1"></i>
                                    Estudo interativo
                                </div>
                                <a href="/capitulos/{{ chapter.id }}" 
                                   class="text-green-400 hover:text-green-300 transition-colors">
                                    <i data-lucide="arrow-right" class="w-4 h-4"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-12 border border-gray-700 p-6 bg-gray-900/20 rounded-lg">
                        <h3 class="text-white font-bold mb-4">🚀 Ações Rápidas</h3>
                        <div class="grid md:grid-cols-3 gap-4">
                            <a href="/estudos" 
                               class="flex items-center gap-3 p-3 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors">
                                <i data-lucide="home" class="w-5 h-5 text-white"></i>
                                <span class="text-white">Dashboard Principal</span>
                            </a>
                            <button onclick="startRandomChapter()" 
                                    class="flex items-center gap-3 p-3 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors">
                                <i data-lucide="shuffle" class="w-5 h-5 text-white"></i>
                                <span class="text-white">Capítulo Aleatório</span>
                            </button>
                            <button onclick="toggleBookmarks()" 
                                    class="flex items-center gap-3 p-3 border border-gray-700 rounded-lg hover:bg-gray-800 transition-colors">
                                <i data-lucide="bookmark" class="w-5 h-5 text-white"></i>
                                <span class="text-white">Favoritos</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/capitulos.js') }}"></script>
{% endblock %}
